@extends('layouts.master')

@section('title', 'Kitchen Management')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS for Tailwind -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
.workload-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.workload-low { background-color: #10b981; }
.workload-medium { background-color: #f59e0b; }
.workload-high { background-color: #ef4444; }

/* DataTables Tailwind customizations */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    outline: none;
}

.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
    border-color: #3b82f6;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #f9fafb;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
}

.dt-buttons .dt-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
}

.dt-buttons .dt-button:hover {
    background-color: #f9fafb;
}
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Kitchen Management</h1>
        <nav class="flex mt-2" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="#" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <i class="fas fa-home mr-2"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500">Kitchen Management</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
    <div class="mt-4 sm:mt-0">
        <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200" id="add-kitchen-btn">
            <i class="fas fa-plus mr-2"></i>
            Add Kitchen
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">Total Kitchens</p>
                    <p class="text-white text-3xl font-bold" id="total-kitchens">0</p>
                </div>
                <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-utensils text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">Active Kitchens</p>
                    <p class="text-white text-3xl font-bold" id="active-kitchens">0</p>
                </div>
                <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-yellow-100 text-sm font-medium">Operating Now</p>
                    <p class="text-white text-3xl font-bold" id="operating-kitchens">0</p>
                </div>
                <div class="bg-yellow-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">Active KOTs</p>
                    <p class="text-white text-3xl font-bold" id="active-kots">0</p>
                </div>
                <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-receipt text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Toggle and Kitchens Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Kitchen Management</h3>
        <div class="flex space-x-2">
            <button type="button" class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors view-toggle active" data-view="table">
                <i class="fas fa-table mr-2"></i>Table
            </button>
            <button type="button" class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors view-toggle" data-view="card">
                <i class="fas fa-th-large mr-2"></i>Cards
            </button>
        </div>
    </div>
    <!-- Table View -->
    <div class="p-6" id="table-view">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="kitchens-table" data-page-length='25'>
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Station Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operating</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Workload</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Card View -->
    <div class="p-6 hidden" id="card-view">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="kitchens-cards">
            <!-- Kitchen cards will be loaded here -->
        </div>
    </div>
</div>

<!-- Add Kitchen Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="addKitchenModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Add New Kitchen</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeAddModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="addKitchenForm" class="mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="branch_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Branch <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="branch_id" name="branch_id" required>
                        <option value="">Select Branch</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Kitchen Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="name" name="name" required>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">Kitchen Code</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="code" name="code">
                </div>

                <div>
                    <label for="station_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Station Type <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="station_type" name="station_type" required>
                        <option value="">Select Station Type</option>
                        <option value="hot">Hot Station</option>
                        <option value="cold">Cold Station</option>
                        <option value="grill">Grill Station</option>
                        <option value="fryer">Fryer Station</option>
                        <option value="salad">Salad Station</option>
                        <option value="dessert">Dessert Station</option>
                        <option value="beverage">Beverage Station</option>
                        <option value="prep">Prep Station</option>
                        <option value="main">Main Kitchen</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="description" name="description" rows="3"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div>
                    <label for="max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent Orders</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                </div>

                <div>
                    <label for="average_prep_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">Avg Prep Time (minutes)</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                </div>

                <div>
                    <label for="manager_id" class="block text-sm font-medium text-gray-700 mb-2">Manager</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="manager_id" name="manager_id">
                        <option value="">Select Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="display_order" class="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="display_order" name="display_order" min="0">
                </div>

                <div class="flex items-center mt-8">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="is_active" name="is_active" checked>
                    <label for="is_active" class="ml-2 block text-sm text-gray-700">Active</label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="cancelAddModal">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    Create Kitchen
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Kitchen Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="editKitchenModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Edit Kitchen</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeEditModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="editKitchenForm" class="mt-6">
            <input type="hidden" id="edit_kitchen_id" name="kitchen_id">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="edit_branch_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Branch <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_branch_id" name="branch_id" required>
                        <option value="">Select Branch</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Kitchen Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_name" name="name" required>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="edit_code" class="block text-sm font-medium text-gray-700 mb-2">Kitchen Code</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_code" name="code">
                </div>

                <div>
                    <label for="edit_station_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Station Type <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_station_type" name="station_type" required>
                        <option value="">Select Station Type</option>
                        <option value="hot">Hot Station</option>
                        <option value="cold">Cold Station</option>
                        <option value="grill">Grill Station</option>
                        <option value="fryer">Fryer Station</option>
                        <option value="salad">Salad Station</option>
                        <option value="dessert">Dessert Station</option>
                        <option value="beverage">Beverage Station</option>
                        <option value="prep">Prep Station</option>
                        <option value="main">Main Kitchen</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <div class="mt-6">
                <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_description" name="description" rows="3"></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div>
                    <label for="edit_max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent Orders</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_max_concurrent_orders" name="max_concurrent_orders" min="1" max="100">
                </div>

                <div>
                    <label for="edit_average_prep_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">Avg Prep Time (minutes)</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_average_prep_time_minutes" name="average_prep_time_minutes" min="1" max="300">
                </div>

                <div>
                    <label for="edit_manager_id" class="block text-sm font-medium text-gray-700 mb-2">Manager</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_manager_id" name="manager_id">
                        <option value="">Select Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager->id }}">{{ $manager->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="edit_display_order" class="block text-sm font-medium text-gray-700 mb-2">Display Order</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_display_order" name="display_order" min="0">
                </div>

                <div class="flex items-center mt-8">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="edit_is_active" name="is_active">
                    <label for="edit_is_active" class="ml-2 block text-sm text-gray-700">Active</label>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="cancelEditModal">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    Update Kitchen
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS for Tailwind -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.colVis.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.tailwindcss.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#kitchens-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("kitchens.index") }}',
                type: 'GET'
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'name', name: 'name' },
                { data: 'code', name: 'code' },
                { data: 'station_type', name: 'station_type' },
                { data: 'branch_name', name: 'branch.name' },
                { data: 'manager_name', name: 'manager.name' },
                { data: 'status', name: 'is_active' },
                { data: 'operating_status', name: 'operating_status' },
                { data: 'workload', name: 'workload' },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[9, 'desc']], // Order by created_at descending
            language: {
                processing: '<div class="flex items-center justify-center"><i class="fas fa-spinner fa-spin mr-2"></i>Loading...</div>'
            }
        });
    }

    // Initialize table
    initializeDataTable();

    // Load initial statistics
    updateStatistics();

    // Update statistics function
    function updateStatistics() {
        $.ajax({
            url: '{{ route("kitchens.index") }}',
            method: 'GET',
            data: { view: 'cards' },
            success: function(response) {
                if (response.success && response.data) {
                    const kitchens = response.data;
                    const totalKitchens = kitchens.length;
                    const activeKitchens = kitchens.filter(k => k.is_active).length;
                    const operatingKitchens = kitchens.filter(k => k.is_operating).length;
                    const totalWorkload = kitchens.reduce((sum, k) => sum + (k.current_workload || 0), 0);

                    $('#total-kitchens').text(totalKitchens);
                    $('#active-kitchens').text(activeKitchens);
                    $('#operating-kitchens').text(operatingKitchens);
                    $('#active-kots').text(totalWorkload);
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Modal functionality
    function showModal(modalId) {
        $('#' + modalId).removeClass('hidden');
    }

    function hideModal(modalId) {
        $('#' + modalId).addClass('hidden');
    }

    // Add Kitchen Modal functionality
    $('#add-kitchen-btn').on('click', function() {
        showModal('addKitchenModal');
    });

    // Close modal handlers
    $('#closeAddModal, #cancelAddModal').on('click', function() {
        hideModal('addKitchenModal');
    });

    $('#closeEditModal, #cancelEditModal').on('click', function() {
        hideModal('editKitchenModal');
    });

    // Close modal when clicking outside
    $('#addKitchenModal, #editKitchenModal').on('click', function(e) {
        if (e.target === this) {
            hideModal(this.id);
        }
    });

    // Handle Add Kitchen Form Submission
    $('#addKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Creating...');

        const formData = new FormData(this);

        $.ajax({
            url: '{{ route("kitchens.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Kitchen created successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reset form and close modal
                    $('#addKitchenForm')[0].reset();
                    hideModal('addKitchenModal');

                    // Refresh table and statistics
                    table.draw();
                    updateStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to create kitchen',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error creating kitchen:', xhr);
                let errorMessage = "Failed to create kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Edit Kitchen functionality
    $(document).on('click', '.edit-kitchen', function() {
        const kitchenId = $(this).data('id');

        // Fetch kitchen data
        $.ajax({
            url: '{{ route("kitchens.show", ":id") }}'.replace(':id', kitchenId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const kitchen = response.data;

                    // Populate edit form
                    $('#edit_kitchen_id').val(kitchen.id);
                    $('#edit_name').val(kitchen.name);
                    $('#edit_code').val(kitchen.code);
                    $('#edit_branch_id').val(kitchen.branch_id);
                    $('#edit_station_type').val(kitchen.station_type);
                    $('#edit_description').val(kitchen.description);
                    $('#edit_max_concurrent_orders').val(kitchen.max_concurrent_orders);
                    $('#edit_average_prep_time_minutes').val(kitchen.average_prep_time_minutes);
                    $('#edit_manager_id').val(kitchen.manager_id);
                    $('#edit_display_order').val(kitchen.display_order);
                    $('#edit_is_active').prop('checked', kitchen.is_active == 1);

                    // Show modal
                    showModal('editKitchenModal');
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to fetch kitchen data',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error fetching kitchen:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to fetch kitchen data',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    });

    // Handle Edit Kitchen Form Submission
    $('#editKitchenForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Updating...');

        const formData = new FormData(this);
        formData.append('_method', 'PUT');
        const kitchenId = $('#edit_kitchen_id').val();

        $.ajax({
            url: '{{ route("kitchens.update", ":id") }}'.replace(':id', kitchenId),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Kitchen updated successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reset form and close modal
                    $('#editKitchenForm')[0].reset();
                    hideModal('editKitchenModal');

                    // Refresh table and statistics
                    table.draw();
                    updateStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to update kitchen',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error updating kitchen:', xhr);
                let errorMessage = "Failed to update kitchen";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Delete Kitchen functionality
    $(document).on('click', '.delete-kitchen', function() {
        const kitchenId = $(this).data('id');
        const kitchenName = $(this).data('name');

        Swal.fire({
            title: 'Are you sure?',
            text: `You want to delete kitchen "${kitchenName}"? This action cannot be undone!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("kitchens.destroy", ":id") }}'.replace(':id', kitchenId),
                    method: 'POST',
                    data: {
                        _method: 'DELETE'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Deleted!',
                                text: 'Kitchen has been deleted successfully',
                                icon: 'success',
                                confirmButtonColor: '#3b82f6'
                            });
                            table.draw();
                            updateStatistics();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Failed to delete kitchen',
                                icon: 'error',
                                confirmButtonColor: '#3b82f6'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error deleting kitchen:', xhr);
                        let errorMessage = "Failed to delete kitchen";

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonColor: '#3b82f6'
                        });
                    }
                });
            }
        });
    });

    // Reset forms when modals are closed
    function resetAddForm() {
        $('#addKitchenForm')[0].reset();
    }

    function resetEditForm() {
        $('#editKitchenForm')[0].reset();
    }

    // View toggle functionality
    $('.view-toggle').on('click', function() {
        const view = $(this).data('view');

        // Update button states
        $('.view-toggle').removeClass('active bg-blue-600 text-white').addClass('bg-white text-gray-700 border-gray-300');
        $(this).removeClass('bg-white text-gray-700 border-gray-300').addClass('active bg-blue-600 text-white border-blue-600');

        if (view === 'table') {
            $('#table-view').removeClass('hidden');
            $('#card-view').addClass('hidden');
        } else {
            $('#table-view').addClass('hidden');
            $('#card-view').removeClass('hidden');
            loadKitchenCards();
        }
    });

    // Load kitchen cards
    function loadKitchenCards() {
        $.ajax({
            url: '{{ route("kitchens.index") }}',
            method: 'GET',
            data: { view: 'cards' },
            success: function(response) {
                if (response.success && response.data) {
                    displayKitchenCards(response.data);
                }
            },
            error: function(xhr) {
                console.error('Error loading kitchen cards:', xhr);
            }
        });
    }

    // Display kitchen cards
    function displayKitchenCards(kitchens) {
        const cardsContainer = $('#kitchens-cards');
        cardsContainer.empty();

        kitchens.forEach(function(kitchen) {
            const card = createKitchenCard(kitchen);
            cardsContainer.append(card);
        });
    }

    // Create kitchen card HTML
    function createKitchenCard(kitchen) {
        const statusBadge = kitchen.is_active ?
            '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>' :
            '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>';

        const operatingBadge = kitchen.is_operating ?
            '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Operating</span>' :
            '<span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Closed</span>';

        return `
            <div class="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">${kitchen.name}</h3>
                        <div class="flex space-x-1">
                            ${statusBadge}
                            ${operatingBadge}
                        </div>
                    </div>

                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Code:</span>
                            <span class="font-medium">${kitchen.code}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Station Type:</span>
                            <span class="font-medium">${kitchen.station_type || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Branch:</span>
                            <span class="font-medium">${kitchen.branch_name || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Manager:</span>
                            <span class="font-medium">${kitchen.manager_name || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Workload:</span>
                            <span class="font-medium">${kitchen.current_workload || 0} orders</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button type="button" class="flex-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors edit-kitchen"
                                data-id="${kitchen.id}">
                            <i class="fas fa-edit mr-1"></i> Edit
                        </button>
                        <button type="button" class="flex-1 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors delete-kitchen"
                                data-id="${kitchen.id}" data-name="${kitchen.name}">
                            <i class="fas fa-trash mr-1"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Reset forms when modals are hidden
    $('#addKitchenModal').on('click', function(e) {
        if (e.target === this) {
            resetAddForm();
        }
    });

    $('#editKitchenModal').on('click', function(e) {
        if (e.target === this) {
            resetEditForm();
        }
    });
});
</script>
@endpush
