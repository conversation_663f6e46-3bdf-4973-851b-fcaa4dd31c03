@extends('layouts.master')

@section('title', 'Menu Categories')

@push('styles')
<!-- DataTables Tailwind CSS CDN -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css" rel="stylesheet">
@endpush

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">Menu Categories</h1>
            <button 
                onclick="openCreateModal()" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Category
            </button>
        </div>

        <!-- Filters -->
        <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="search-input" placeholder="Search categories..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Menu</label>
                <select id="menu-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Menus</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>
            </div>
        </div>

        <!-- DataTable -->
        <div class="overflow-x-auto">
            <table id="categories-table" class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Menu</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTable will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Category Modal -->
<div id="create-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Create New Category</h3>
            <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="create-category-form" action="{{ route('categories.store') }}" method="POST">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category Name *</label>
                    <input type="text" name="name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Code *</label>
                    <input type="text" name="code" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Menu *</label>
                    <select name="menu_id" id="create_menu_id" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select a menu</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                    <input type="number" name="sort_order" value="0" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="1" checked class="mr-2">
                            <span class="text-sm text-gray-700">Active</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="0" class="mr-2">
                            <span class="text-sm text-gray-700">Inactive</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-6">
                <button type="button" onclick="closeCreateModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                    Cancel
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200">
                    Create Category
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Category Modal -->
<div id="edit-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Edit Category</h3>
            <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="edit-category-form" method="POST">
            @csrf
            @method('PUT')
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category Name *</label>
                    <input type="text" name="name" id="edit_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Code *</label>
                    <input type="text" name="code" id="edit_code" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Menu *</label>
                    <select name="menu_id" id="edit_menu_id" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select a menu</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                    <input type="number" name="sort_order" id="edit_sort_order" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" id="edit_description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="1" id="edit_active" class="mr-2">
                            <span class="text-sm text-gray-700">Active</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="0" id="edit_inactive" class="mr-2">
                            <span class="text-sm text-gray-700">Inactive</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-6">
                <button type="button" onclick="closeEditModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                    Cancel
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200">
                    Update Category
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Category Modal -->
<div id="delete-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="text-center">
            <svg class="mx-auto mb-4 w-14 h-14 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500">Are you sure you want to delete this category?</h3>
            <p class="text-sm text-gray-400 mb-4">Category: <span id="delete-category-name" class="font-semibold"></span></p>
            <div class="flex justify-center space-x-3">
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                    Cancel
                </button>
                <button id="confirm-delete-btn" 
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition duration-200">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Category Modal -->
<div id="view-category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Category Details</h3>
            <button onclick="closeViewModal()" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div id="view-category-content" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Content will be populated by JavaScript -->
        </div>
        
        <div class="flex justify-end pt-6">
            <button onclick="closeViewModal()" 
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                Close
            </button>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>

<script>
let menusData = [];
let categoriesTable;

document.addEventListener('DOMContentLoaded', function() {
    // Load menus data
    loadMenusData();
    
    // Initialize DataTable
    initializeDataTable();
    
    // Set up form handlers
    setupFormHandlers();
    
    // Set up filters
    setupFilters();
});

function loadMenusData() {
    fetch('{{ route("categories.menus-list") }}', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            menusData = data.data;
            updateMenuDropdowns();
            updateMenuFilter();
        }
    })
    .catch(error => {
        console.error('Error loading menus:', error);
    });
}

function updateMenuDropdowns() {
    const createSelect = document.getElementById('create_menu_id');
    const editSelect = document.getElementById('edit_menu_id');
    
    [createSelect, editSelect].forEach(select => {
        if (select) {
            select.innerHTML = '<option value="">Select a menu</option>';
            menusData.forEach(menu => {
                select.innerHTML += `<option value="${menu.id}">${menu.name}</option>`;
            });
        }
    });
}

function updateMenuFilter() {
    const filterSelect = document.getElementById('menu-filter');
    if (filterSelect) {
        filterSelect.innerHTML = '<option value="">All Menus</option>';
        menusData.forEach(menu => {
            filterSelect.innerHTML += `<option value="${menu.id}">${menu.name}</option>`;
        });
    }
}

function initializeDataTable() {
    categoriesTable = $('#categories-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("categories.index") }}',
            data: function(d) {
                d.menu_id = document.getElementById('menu-filter').value;
                d.status = document.getElementById('status-filter').value;
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'menu_name', name: 'menu.name' },
            { data: 'description', name: 'description', orderable: false },
            { data: 'status', name: 'status', orderable: false,searchable: false },
            { data: 'sort_order', name: 'sort_order' },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'rtip',
        language: {
            processing: '<div class="flex justify-center items-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>',
            emptyTable: 'No categories found',
            zeroRecords: 'No matching categories found'
        }
    });
}

function setupFilters() {
    // Search input
    document.getElementById('search-input').addEventListener('keyup', function() {
        categoriesTable.search(this.value).draw();
    });
    
    // Menu filter
    document.getElementById('menu-filter').addEventListener('change', function() {
        categoriesTable.ajax.reload();
    });
    
    // Status filter
    document.getElementById('status-filter').addEventListener('change', function() {
        categoriesTable.ajax.reload();
    });
}

function setupFormHandlers() {
    // Create form
    document.getElementById('create-category-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'Category created successfully');
    });
    
    // Edit form
    document.getElementById('edit-category-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm(this, 'Category updated successfully');
    });
}

function submitForm(form, successMessage) {
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(successMessage, 'success');
            categoriesTable.ajax.reload();
            closeAllModals();
            form.reset();
        } else {
            showNotification(data.message || 'An error occurred', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while processing your request', 'error');
    });
}

// Modal functions
function openCreateModal() {
    document.getElementById('create-category-modal').classList.remove('hidden');
}

function closeCreateModal() {
    document.getElementById('create-category-modal').classList.add('hidden');
    document.getElementById('create-category-form').reset();
}

function editCategory(id, categoryData) {
    // Populate form fields
    document.getElementById('edit_name').value = categoryData.name || '';
    document.getElementById('edit_code').value = categoryData.code || '';
    document.getElementById('edit_menu_id').value = categoryData.menu_id || '';
    document.getElementById('edit_description').value = categoryData.description || '';
    document.getElementById('edit_sort_order').value = categoryData.sort_order || 0;
    
    // Set status radio buttons
    if (categoryData.is_active == '1') {
        document.getElementById('edit_active').checked = true;
    } else {
        document.getElementById('edit_inactive').checked = true;
    }
    
    // Set form action
    document.getElementById('edit-category-form').action = `{{ route('categories.index') }}/${id}`;
    
    // Show modal
    document.getElementById('edit-category-modal').classList.remove('hidden');
}

function closeEditModal() {
    document.getElementById('edit-category-modal').classList.add('hidden');
    document.getElementById('edit-category-form').reset();
}

function deleteCategory(id, categoryName) {
    document.getElementById('delete-category-name').textContent = categoryName;
    
    document.getElementById('confirm-delete-btn').onclick = function() {
        fetch(`{{ route('categories.index') }}/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Category deleted successfully', 'success');
                categoriesTable.ajax.reload();
                closeDeleteModal();
            } else {
                showNotification(data.message || 'Error deleting category', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while deleting the category', 'error');
        });
    };
    
    document.getElementById('delete-category-modal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('delete-category-modal').classList.add('hidden');
}

function viewCategory(id, categoryData) {
    const content = document.getElementById('view-category-content');
    const menuName = menusData.find(menu => menu.id == categoryData.menu_id)?.name || 'N/A';
    const status = categoryData.is_active == '1' ? 'Active' : 'Inactive';
    const statusClass = categoryData.is_active == '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    
    content.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <p class="text-gray-900">${categoryData.name || 'N/A'}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Code</label>
            <p class="text-gray-900">${categoryData.code || 'N/A'}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Menu</label>
            <p class="text-gray-900">${menuName}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
            <p class="text-gray-900">${categoryData.sort_order || 0}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">${status}</span>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Created At</label>
            <p class="text-gray-900">${categoryData.created_at || 'N/A'}</p>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <p class="text-gray-900">${categoryData.description || 'No description available'}</p>
        </div>
    `;
    
    document.getElementById('view-category-modal').classList.remove('hidden');
}

function closeViewModal() {
    document.getElementById('view-category-modal').classList.add('hidden');
}

function closeAllModals() {
    closeCreateModal();
    closeEditModal();
    closeDeleteModal();
    closeViewModal();
}

// Notification function
function showNotification(message, type = 'info') {
    const colors = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
    };
    
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Close modals when clicking outside
window.addEventListener('click', function(event) {
    const modals = ['create-category-modal', 'edit-category-modal', 'delete-category-modal', 'view-category-modal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            modal.classList.add('hidden');
        }
    });
});
</script>
@endpush