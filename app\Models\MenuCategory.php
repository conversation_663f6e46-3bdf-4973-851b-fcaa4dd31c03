<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'menu_id',
        'parent_category_id',
        'name',
        'icon',
        'code',
        'description',
        'image_url',
        'sort_order',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function parentCategory()
    {
        return $this->belongsTo(MenuCategory::class, 'parent_category_id');
    }

    public function childCategories()
    {
        return $this->hasMany(MenuCategory::class, 'parent_category_id');
    }

    public function menuItems()
    {
        return $this->hasMany(MenuItem::class, 'category_id');
    }
}