<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Web\RestaurantTableController;
use Modules\Reservation\Http\Controllers\Web\QRTestController;
use Modules\Reservation\Http\Controllers\Web\ReservationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


// QR Code Test Routes
Route::prefix('reservation/qr-test')->name('reservation.qr.')->group(function () {
    Route::get('/', [QRTestController::class, 'index'])->name('test');
    Route::post('/generate', [QRTestController::class, 'generateQR'])->name('generate');
    Route::post('/validate', [QRTestController::class, 'validateQR'])->name('validate');
    Route::post('/custom', [QRTestController::class, 'generateCustomQR'])->name('custom');
    Route::post('/batch', [QRTestController::class, 'batchGenerate'])->name('batch');
    Route::get('/table-info', [QRTestController::class, 'getTableInfo'])->name('table-info');
});

Route::prefix('reservation')->group(function () {
    // Dashboard
    Route::get('/', [ReservationController::class, 'dashboard'])->name('reservation.dashboard');
    
    // DataTable Views
    Route::get('/reservations', [ReservationController::class, 'reservationsIndex'])->name('reservation.reservations');
    
    // CRUD Routes for Reservations
    Route::resource('reservations', ReservationController::class)->except(['index']);
    
    // CRUD Routes for Waiter Requests
    Route::get('/waiter-requests', [ReservationController::class, 'waiterRequestsIndex'])->name('reservation.waiter-requests');
    Route::resource('waiter-requests', ReservationController::class)->except(['index']);
    
    // API Routes for fetching lists
    Route::get('/api/customers', [ReservationController::class, 'getCustomers']);
    Route::get('/api/areas', [ReservationController::class, 'getAreas']);
    Route::get('/api/tables', [ReservationController::class, 'getTables']);
    Route::get('/api/areas/{id}/tables', [ReservationController::class, 'getAvailableTablesByArea']);
    Route::get('/api/waiters', [ReservationController::class, 'getWaiters']);
    Route::get('/api/reservation-statuses', [ReservationController::class, 'getReservationStatuses']);
});

// Separate Areas Management
Route::middleware('auth')->prefix('areas')->group(function () {
    Route::get('/', [ReservationController::class, 'areasIndex'])->name('areas.index');
    Route::resource('areas', ReservationController::class)->except(['index']);
});

// Separate Tables Management  
Route::middleware('auth')->prefix('tables')->group(function () {
    Route::get('/', [ReservationController::class, 'tablesIndex'])->name('tables.index');
    
    // Explicit CRUD routes that match controller methods
    Route::post('/', [ReservationController::class, 'tablesStore'])->name('tables.store');
    Route::get('/{id}', [ReservationController::class, 'tablesShow'])->name('tables.show');
    Route::get('/{id}/edit', [ReservationController::class, 'tablesEdit'])->name('tables.edit');
    Route::put('/{id}', [ReservationController::class, 'tablesUpdate'])->name('tables.update');
    Route::delete('/{id}', [ReservationController::class, 'tablesDestroy'])->name('tables.destroy');
    
    // DataTable AJAX endpoint
    Route::get('/data/tables', [ReservationController::class, 'tablesDataTable'])->name('reservation.tables.data');
    
    // QR Code Management
    Route::post('/{id}/generate-qr', [ReservationController::class, 'generateTableQR'])->name('tables.generate-qr');
    Route::post('/{id}/regenerate-qr', [ReservationController::class, 'regenerateTableQR'])->name('tables.regenerate-qr');
    Route::post('/{id}/set-manual-qr', [ReservationController::class, 'setManualTableQR'])->name('tables.set-manual-qr');
});

