<?php

namespace Modules\Transaction\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StoreTransactionRequest;
use Modules\Transaction\Http\Requests\UpdateTransactionRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use App\Models\Order;

class TransactionWebController extends Controller
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of transactions.
     */
    public function index(): View
    {
        return view('transaction::transactions.index');
    }

    /**
     * Show the form for creating a new transaction.
     */
    public function create(): View
    {
        $orders = Order::whereDoesntHave('transaction')
            ->with(['customer', 'table'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('transaction::transactions.create', compact('orders'));
    }

    /**
     * Store a newly created transaction.
     */
    public function store(StoreTransactionRequest $request): RedirectResponse
    {
        try {
            $validatedData = $request->validated();
            
            // Check if this is an order-based transaction or standalone
            if (!empty($validatedData['order_id'])) {
                // Order-based transaction
                $order = Order::findOrFail($validatedData['order_id']);
                $transaction = $this->transactionService->createTransactionFromOrder($order);
            } else {
                // Standalone transaction
                $transaction = $this->transactionService->createStandaloneTransaction($validatedData);
            }

            return redirect()
                ->route('transactions.show', $transaction->id)
                ->with('success', 'Transaction created successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create transaction: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified transaction.
     */
    public function show(int $id): View
    {
        $transaction = $this->transactionService->getTransactionById($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        return view('transaction::transactions.show', compact('transaction'));
    }

    /**
     * Show the form for editing the specified transaction.
     */
    public function edit(int $id): View
    {
        $transaction = $this->transactionService->getTransactionById($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        return view('transaction::transactions.edit', compact('transaction'));
    }

    /**
     * Update the specified transaction.
     */
    public function update(UpdateTransactionRequest $request, int $id): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return redirect()
                    ->route('transactions.index')
                    ->with('error', 'Transaction not found.');
            }

            // Update transaction notes if provided
            if ($request->has('notes')) {
                $transaction->update([
                    'notes' => $request->notes,
                    'updated_by' => auth()->id(),
                ]);
            }

            return redirect()
                ->route('transactions.show', $transaction->id)
                ->with('success', 'Transaction updated successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update transaction: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified transaction (void).
     */
    public function destroy(int $id, Request $request): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return redirect()
                    ->route('transactions.index')
                    ->with('error', 'Transaction not found.');
            }

            $reason = $request->input('reason', 'Transaction voided by user');
            $this->transactionService->voidTransaction($transaction, $reason);

            return redirect()
                ->route('transactions.index')
                ->with('success', 'Transaction voided successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to void transaction: ' . $e->getMessage());
        }
    }

    /**
     * Update transaction status.
     */
    public function updateStatus(int $id): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return redirect()
                    ->route('transactions.index')
                    ->with('error', 'Transaction not found.');
            }

            $this->transactionService->updateTransactionStatus($transaction);

            return redirect()
                ->route('transactions.show', $transaction->id)
                ->with('success', 'Transaction status updated successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to update transaction status: ' . $e->getMessage());
        }
    }

    /**
     * Show transaction statistics page.
     */
    public function statistics(): View
    {
        return view('transaction::transactions.statistics');
    }

    /**
     * Show due transactions page.
     */
    public function due(): View
    {
        return view('transaction::transactions.due');
    }
}