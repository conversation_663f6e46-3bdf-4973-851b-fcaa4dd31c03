<?php

namespace Modules\Menu\Services;

use App\Models\MenuItemAddon;
use Illuminate\Pagination\LengthAwarePaginator;

class AddonService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get addons for a specific branch with pagination and filtering.
     */
    public function getAddonsForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuItemAddon::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_item_id'])) {
            $query->where('menu_item_id', $filters['menu_item_id']);
        }

        if (isset($filters['addon_group_name'])) {
            $query->where('addon_group_name', $filters['addon_group_name']);
        }

        if (isset($filters['is_required'])) {
            $query->where('is_required', $filters['is_required']);
        }

        if (isset($filters['min_price'])) {
            $query->where('price', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $query->where('price', '<=', $filters['max_price']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('addon_group_name', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get addon by ID with relationships.
     */
    public function getAddonById(int $id): ?MenuItemAddon
    {
        return MenuItemAddon::with(['menuItem'])
            ->find($id);
    }

    /**
     * Get addon by ID for a specific branch.
     */
    public function getAddonByIdForBranch(int $id, int $branchId): ?MenuItemAddon
    {
        return MenuItemAddon::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Create a new addon.
     */
    public function createAddon(array $data): MenuItemAddon
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateAddonCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        return MenuItemAddon::create($data);
    }

    /**
     * Update an existing addon.
     */
    public function updateAddon(int $id, array $data): ?MenuItemAddon
    {
        $addon = MenuItemAddon::find($id);
        
        if (!$addon) {
            return null;
        }

        $addon->update($data);
        
        return $addon->fresh(['menuItem']);
    }

    /**
     * Update an existing addon for a specific branch.
     */
    public function updateAddonForBranch(int $id, array $data, int $branchId): ?MenuItemAddon
    {
        $addon = MenuItemAddon::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$addon) {
            return null;
        }

        $addon->update($data);
        
        return $addon->fresh(['menuItem']);
    }

    /**
     * Delete an addon.
     */
    public function deleteAddon(int $id): bool
    {
        $addon = MenuItemAddon::find($id);
        
        if (!$addon) {
            return false;
        }

        return $addon->delete();
    }

    /**
     * Delete an addon for a specific branch.
     */
    public function deleteAddonForBranch(int $id, int $branchId): bool
    {
        $addon = MenuItemAddon::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$addon) {
            return false;
        }

        return $addon->delete();
    }

    /**
     * Get addons for a menu item.
     */
    public function getAddonsForMenuItem(int $menuItemId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItemAddon::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get addons grouped by addon group name for a menu item.
     */
    public function getGroupedAddonsForMenuItem(int $menuItemId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItemAddon::where('menu_item_id', $menuItemId)
            ->orderBy('addon_group_name')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('addon_group_name');
    }

    /**
     * Get required addons for a menu item.
     */
    public function getRequiredAddonsForMenuItem(int $menuItemId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItemAddon::where('menu_item_id', $menuItemId)
            ->where('is_required', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Create addon for web interface.
     */
    public function createAddonForWeb(array $data): MenuItemAddon
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateAddonCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        $addon = MenuItemAddon::create($data);
        return $addon->load(['menuItem']);
    }

    /**
     * Update addon for web interface.
     */
    public function updateAddonForWeb(int $id, array $data, int $branchId): ?MenuItemAddon
    {
        return $this->updateAddonForBranch($id, $data, $branchId);
    }

    /**
     * Get addons for DataTable.
     */
    public function getAddonsForDataTable(int $branchId, \Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse
    {
        $query = MenuItemAddon::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search
        if ($request->has('search') && !empty($request->search['value'])) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('addon_group_name', 'like', "%{$search}%");
            });
        }

        // Apply ordering
        if ($request->has('order')) {
            $columns = ['id', 'name', 'code', 'addon_group_name', 'price', 'is_active', 'created_at'];
            $orderColumn = $columns[$request->order[0]['column']] ?? 'id';
            $orderDirection = $request->order[0]['dir'] ?? 'asc';
            $query->orderBy($orderColumn, $orderDirection);
        }

        $totalRecords = $query->count();
        
        // Apply pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $addons = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = $addons->map(function ($addon, $index) use ($start) {
            return [
                'DT_RowIndex' => $start + $index + 1,
                'name' => $addon->name,
                'price' => number_format($addon->price, 2) . ' ريال',
                'is_active' => $addon->is_active ? 
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' : 
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>',
                'created_at' => $addon->created_at->format('Y-m-d H:i:s'),
                'action' => $this->getActionButtons($addon->id)
            ];
        });

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate action buttons for DataTable.
     */
    private function getActionButtons(int $id): string
    {
        return '
            <div class="flex space-x-2">
                <button type="button" class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors duration-200 show-addon" data-id="' . $id . '" title="عرض">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <button type="button" class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-colors duration-200 edit-addon" data-id="' . $id . '" title="تعديل">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
                <button type="button" class="inline-flex items-center px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded transition-colors duration-200 delete-addon" data-id="' . $id . '" title="حذف">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        ';
    }
}