<?php

namespace Modules\Transaction\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Modules\Transaction\Services\PaymentService;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StorePaymentRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class PaymentWebController extends Controller
{
    protected PaymentService $paymentService;
    protected TransactionService $transactionService;

    public function __construct(PaymentService $paymentService, TransactionService $transactionService)
    {
        $this->paymentService = $paymentService;
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of payments.
     */
    public function index(): View
    {
        return view('transaction::payments.index');
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        $transactionId = $request->get('transaction_id');
        $transaction = null;

        if ($transactionId) {
            $transaction = $this->transactionService->getTransactionById($transactionId);
        }

        $paymentMethods = $this->paymentService->getPaymentMethods();
        $dueTransactions = $this->transactionService->getDueTransactions(['limit' => 50]);

        return view('transaction::payments.create', compact('transaction', 'paymentMethods', 'dueTransactions'));
    }

    /**
     * Store a newly created payment.
     */
    public function store(StorePaymentRequest $request): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($request->transaction_id);

            if (!$transaction) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->with('error', 'Transaction not found.');
            }

            $payment = $this->paymentService->processPayment($transaction, $request->validated());

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment processed successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(int $id): View
    {
        $payment = $this->paymentService->getPaymentById($id);

        if (!$payment) {
            abort(404, 'Payment not found');
        }

        return view('transaction::payments.show', compact('payment'));
    }

    /**
     * Cancel the specified payment.
     */
    public function cancel(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $reason = $request->input('reason', 'Payment cancelled by user');
            $this->paymentService->cancelPayment($payment, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment cancelled successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Refund the specified payment.
     */
    public function refund(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $refundAmount = $request->input('refund_amount');
            $reason = $request->input('reason', 'Payment refunded by user');
            
            $this->paymentService->refundPayment($payment, $refundAmount, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment refunded successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to refund payment: ' . $e->getMessage());
        }
    }

    /**
     * Show payment statistics page.
     */
    public function statistics(): View
    {
        return view('transaction::payments.statistics');
    }

    /**
     * Show payment methods page.
     */
    public function methods(): View
    {
        return view('transaction::payments.methods');
    }

    /**
     * Show refunds page.
     */
    public function refunds(): View
    {
        return view('transaction::payments.refunds');
    }

    /**
     * Show payments for a specific transaction.
     */
    public function byTransaction(int $transactionId): View
    {
        $transaction = $this->transactionService->getTransactionById($transactionId);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        return view('transaction::payments.by-transaction', compact('transaction'));
    }
}