@extends('layouts.master')

@section('title', 'إدارة الفروع')

@section('content')
<!-- Page Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-store text-white text-sm"></i>
                    </div>
                    إدارة الفروع
                </h1>
                <p class="mt-1 text-sm text-gray-500">إدارة جميع فروع المستأجرين في النظام</p>
            </div>
            <a href="{{ route('branches.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-plus"></i>
                إضافة فرع جديد
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Filters Section -->
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-filter text-green-600"></i>
                فلاتر البحث
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div>
                    <label for="tenantFilter" class="block text-sm font-medium text-gray-700 mb-2">المستأجر</label>
                    <select id="tenantFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">جميع المستأجرين</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- DataTable Controls -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- Global Search -->
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="globalSearch" placeholder="البحث في الفروع..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>

                <!-- Export Buttons -->
                <div class="flex gap-2">
                    <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-excel"></i>
                        Excel
                    </button>
                    <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-pdf"></i>
                        PDF
                    </button>
                    <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table id="branches-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الاسم</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الكود</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المستأجر</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">العنوان</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">معلومات الاتصال</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">مدير الفرع</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الخدمات</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحالة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">تاريخ الإنشاء</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- View Branch Modal -->
<div id="viewModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-4xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-eye text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">عرض تفاصيل الفرع</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('viewModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6 max-h-96 overflow-y-auto">
            <div id="viewBranchContent" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('viewModal')">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Branch Modal -->
<div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-4xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-edit text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">تعديل الفرع</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('editModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6 max-h-96 overflow-y-auto">
            <form id="editBranchForm">
                <div id="editBranchContent" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Content will be loaded dynamically -->
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('editModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" id="saveEditChanges" class="px-6 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    .contact-info div {
        margin-bottom: 2px;
        font-size: 0.9em;
    }
    .contact-info i {
        width: 15px;
        margin-right: 5px;
    }

    /* Custom DataTable styling for Tailwind */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #059669;
        border-color: #059669;
        color: white;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Modal Functions -->
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['viewModal', 'editModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = ['viewModal', 'editModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (!modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});
</script>

<script>
$(document).ready(function() {
    let table = $('#branches-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("branches.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.tenant_id = $('#tenantFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'tenant_name', name: 'tenant.name' },
            { data: 'address', name: 'address' },
            { data: 'contact_info', name: 'contact_info', orderable: false, searchable: false },
            { data: 'manager_name', name: 'manager_name' },
            { data: 'services', name: 'services', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[8, 'desc']],
   
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Enhanced Search and Filter Functionality
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Filter change handlers
    $('#statusFilter, #tenantFilter').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#statusFilter, #tenantFilter').val('');
        table.draw();
    });

    // Status change functionality
    window.changeStatus = function(branchId, currentStatus) {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';
        const actionText = newStatus === 'active' ? 'تفعيل الفرع' : 'إلغاء تفعيل الفرع';
        
        Swal.fire({
            title: 'تأكيد العملية',
            text: `هل تريد ${actionText}؟`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: newStatus === 'active' ? '#28a745' : '#ffc107',
            cancelButtonColor: '#6c757d',
            confirmButtonText: `نعم، ${statusText}`,
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'جاري التحديث...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `/admin/branches/${branchId}/status`,
                    type: 'PATCH',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        status: newStatus
                    },
                    success: function(response) {
                        table.draw();
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: response.message || 'تم تغيير حالة الفرع بنجاح',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr) {
                        let message = xhr.responseJSON?.message || 'حدث خطأ أثناء تغيير الحالة';
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: message
                        });
                    }
                });
            }
        });
    };

    // Delete functionality
    window.deleteBranch = function(branchId, branchName) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: `سيتم حذف الفرع "${branchName || 'هذا الفرع'}" نهائياً`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'جاري الحذف...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route("branches.destroy", ":id") }}'.replace(':id', branchId),
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        table.draw();
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الحذف',
                            text: response.message || 'تم حذف الفرع بنجاح',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr) {
                        let message = xhr.responseJSON?.message || 'حدث خطأ أثناء الحذف';
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: message
                        });
                    }
                });
            }
        });
    };

    // View Branch functionality
    window.viewBranch = function(branchId) {
        $.ajax({
            url: '{{ route("branches.show", ":id") }}'.replace(':id', branchId),
            type: 'GET',
            success: function(response) {
                let content = `
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">المعلومات الأساسية</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">اسم الفرع</label>
                                    <p class="text-gray-800">${response.name || 'غير محدد'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">كود الفرع</label>
                                    <p class="text-gray-800">${response.code || 'غير محدد'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">المستأجر</label>
                                    <p class="text-gray-800">${response.tenant?.name || 'غير محدد'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">الحالة</label>
                                    <span class="px-2 py-1 text-xs rounded-full ${response.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                        ${response.status === 'active' ? 'نشط' : 'غير نشط'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">العنوان ومعلومات الاتصال</h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">العنوان</label>
                                    <p class="text-gray-800">${response.address || 'غير محدد'}</p>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600">رقم الهاتف</label>
                                        <p class="text-gray-800">${response.phone || 'غير محدد'}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600">البريد الإلكتروني</label>
                                        <p class="text-gray-800">${response.email || 'غير محدد'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">معلومات إضافية</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">اسم المدير</label>
                                    <p class="text-gray-800">${response.manager_name || 'غير محدد'}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">تاريخ الإنشاء</label>
                                    <p class="text-gray-800">${response.created_at ? new Date(response.created_at).toLocaleDateString('ar-SA') : 'غير محدد'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#viewBranchContent').html(content);
                openModal('viewModal');
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء تحميل بيانات الفرع'
                });
            }
        });
    };

    // Edit Branch functionality
    window.editBranch = function(branchId) {
        $.ajax({
            url: '{{ route("branches.edit", ":id") }}'.replace(':id', branchId),
            type: 'GET',
            success: function(response) {
                let content = `
                    <input type="hidden" id="editBranchId" value="${response.id}">
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">المعلومات الأساسية</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم الفرع *</label>
                                    <input type="text" id="editName" value="${response.name || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">كود الفرع *</label>
                                    <input type="text" id="editCode" value="${response.code || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم المدير</label>
                                    <input type="text" id="editManagerName" value="${response.manager_name || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                                    <select id="editStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="active" ${response.status === 'active' ? 'selected' : ''}>نشط</option>
                                        <option value="inactive" ${response.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">العنوان ومعلومات الاتصال</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                                    <textarea id="editAddress" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">${response.address || ''}</textarea>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                                        <input type="tel" id="editPhone" value="${response.phone || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                                        <input type="email" id="editEmail" value="${response.email || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#editBranchContent').html(content);
                openModal('editModal');
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء تحميل بيانات الفرع للتعديل'
                });
            }
        });
    };

    // Save edit changes
    $('#saveEditChanges').click(function() {
        const branchId = $('#editBranchId').val();
        const formData = {
            name: $('#editName').val(),
            code: $('#editCode').val(),
            manager_name: $('#editManagerName').val(),
            status: $('#editStatus').val(),
            address: $('#editAddress').val(),
            phone: $('#editPhone').val(),
            email: $('#editEmail').val(),
            _token: '{{ csrf_token() }}',
            _method: 'PUT'
        };

        $.ajax({
            url: '{{ route("branches.update", ":id") }}'.replace(':id', branchId),
            type: 'POST',
            data: formData,
            success: function(response) {
                closeModal('editModal');
                table.draw();
                Swal.fire({
                    icon: 'success',
                    title: 'تم التحديث',
                    text: 'تم تحديث الفرع بنجاح',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            error: function(xhr) {
                let message = 'حدث خطأ أثناء تحديث الفرع';
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في التحديث',
                    text: message
                });
            }
        });
    });
});

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform transition-all duration-300 translate-x-full`;
    
    // Set background color based on type
    const bgColors = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    };
    
    toast.classList.add(bgColors[type] || bgColors.info);
    toast.textContent = message;
    
    // Add to document
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
</script>
@endpush