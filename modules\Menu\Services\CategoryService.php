<?php

namespace Modules\Menu\Services;

use App\Models\MenuCategory;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get categories for a specific branch with pagination and filtering.
     */
    public function getCategoriesForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuCategory::with(['menu', 'parentCategory', 'childCategories'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_id'])) {
            $query->where('menu_id', $filters['menu_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['parent_category_id'])) {
            if ($filters['parent_category_id'] === 'null') {
                $query->whereNull('parent_category_id');
            } else {
                $query->where('parent_category_id', $filters['parent_category_id']);
            }
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get category by ID with relationships.
     */
    public function getCategoryById(int $id): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->find($id);
    }

    /**
     * Create a new category.
     */
    public function createCategory(array $data): MenuCategory
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateCategoryCode(
                $data['name'], 
                $data['menu_id'] ?? null
            );
        }

        return MenuCategory::create($data);
    }

    /**
     * Update an existing category.
     */
    public function updateCategory(int $id, array $data): ?MenuCategory
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete a category.
     */
    public function deleteCategory(int $id): bool
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Get active categories for a menu.
     */
    public function getActiveCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get root categories for a menu (categories without parent).
     */
    public function getRootCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->whereNull('parent_category_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get category by ID for a specific branch.
     */
    public function getCategoryByIdForBranch(int $id, int $branchId): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Update category for a specific branch.
     */
    public function updateCategoryForBranch(int $id, array $data, int $branchId): ?MenuCategory
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete category for a specific branch.
     */
    public function deleteCategoryForBranch(int $id, int $branchId): bool
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Create category for web interface.
     */
    public function createCategoryForWeb(array $data): MenuCategory
    {
        return $this->createCategory($data);
    }

    /**
     * Update category for web interface.
     */
    public function updateCategoryForWeb(int $id, array $data, int $branchId): ?MenuCategory
    {
        return $this->updateCategoryForBranch($id, $data, $branchId);
    }

    /**
     * Get categories for DataTable display.
     */
    public function getCategoriesForDataTable(int $branchId, $request)
    {
        $query = MenuCategory::with(['menu', 'parentCategory'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search filter
        if ($request->has('search') && $request->search['value']) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply menu filter
        if ($request->has('menu_id') && $request->menu_id) {
            $query->where('menu_id', $request->menu_id);
        }

        // Apply status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active' ? 1 : 0);
        }

        return datatables($query)
            ->addIndexColumn()
            ->addColumn('actions', function ($category) {
                return $this->getActionButtons($category);
            })
            ->editColumn('status', function ($category) {
                return $category->is_active 
                    ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>' 
                    : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>';
            })
            ->addColumn('menu_name', function ($category) {
                return $category->menu ? $category->menu->name : '-';
            })
            ->editColumn('created_at', function ($category) {
                return $category->created_at ? $category->created_at->format('Y-m-d H:i:s') : '-';
            })
            ->rawColumns(['actions', 'status'])
            ->make(true);
    }

    /**
     * Get action buttons for DataTable.
     */
    private function getActionButtons($category)
    {
        $categoryData = json_encode([
            'id' => $category->id,
            'name' => $category->name,
            'code' => $category->code,
            'menu_id' => $category->menu_id,
            'description' => $category->description,
            'sort_order' => $category->sort_order,
            'is_active' => $category->is_active ? '1' : '0'
        ]);

        return '
            <div class="flex space-x-2">
                <button type="button" 
                        onclick="viewCategory(' . $category->id . ')" 
                        class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition ease-in-out duration-150"
                        title="View">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <button type="button" 
                        onclick="editCategory(' . $category->id . ', ' . htmlspecialchars($categoryData, ENT_QUOTES, 'UTF-8') . ')" 
                        class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition ease-in-out duration-150"
                        title="Edit">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
                <button type="button" 
                        onclick="deleteCategory(' . $category->id . ', \'' . addslashes($category->name) . '\')" 
                        class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition ease-in-out duration-150"
                        title="Delete">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>';
    }

    /**
     * Get categories list for dropdowns.
     */
    public function getCategoriesListForBranch(int $branchId, ?int $menuId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = MenuCategory::select('id', 'name', 'menu_id', 'parent_category_id')
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->where('is_active', true)
            ->orderBy('sort_order');

        if ($menuId) {
            $query->where('menu_id', $menuId);
        }

        return $query->get();
    }

    /**
     * Get categories query for custom pagination.
     */
    public function getCategoriesQuery(int $branchId)
    {
        $query = MenuCategory::with(['menu', 'parentCategory'])
            ->withCount(['menuItems', 'childCategories']);
            
        // If branchId is provided and valid, filter by branch
        if ($branchId && $branchId > 0) {
            $query->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }
        
        return $query;
    }
}