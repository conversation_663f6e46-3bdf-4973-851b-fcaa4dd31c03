@extends('layouts.master')

@section('title', 'Payment Details')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Payment Details</h1>
                    <p class="mt-2 text-sm text-gray-600">View detailed information about this payment</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('payments.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Payments
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment Details Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Payment Information</h3>
            </div>
            <div class="p-6">
                <div id="payment-details" class="space-y-6">
                    <!-- Loading state -->
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Details Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6" id="transaction-card" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Related Transaction</h3>
            </div>
            <div class="p-6">
                <div id="transaction-details" class="space-y-4">
                    <!-- Transaction details will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200" id="actions-card" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
            </div>
            <div class="p-6">
                <div class="flex space-x-4">
                    <button id="cancel-payment-btn" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150" style="display: none;">
                        <i class="fas fa-times mr-2"></i> Cancel Payment
                    </button>
                    <button id="refund-payment-btn" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 focus:bg-yellow-700 active:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition ease-in-out duration-150" style="display: none;">
                        <i class="fas fa-undo mr-2"></i> Refund Payment
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    const paymentId = {{ $id }};

    // Load payment details
    function loadPaymentDetails() {
        $.ajax({
            url: `{{ url('api/payments') }}/${paymentId}`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    displayPaymentDetails(response.data);
                    showActionButtons(response.data);
                } else {
                    showError('Payment not found');
                }
            },
            error: function(xhr) {
                console.error('Error loading payment details:', xhr);
                showError('Failed to load payment details');
            }
        });
    }

    // Display payment details
    function displayPaymentDetails(payment) {
        const statusClasses = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'completed': 'bg-green-100 text-green-800',
            'failed': 'bg-red-100 text-red-800',
            'cancelled': 'bg-gray-100 text-gray-800',
            'refunded': 'bg-purple-100 text-purple-800'
        };

        const statusClass = statusClasses[payment.status] || 'bg-gray-100 text-gray-800';

        const detailsHtml = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Payment ID</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.id}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Amount</label>
                        <p class="mt-1 text-lg font-semibold text-gray-900">${payment.formatted_amount || '$' + parseFloat(payment.amount).toFixed(2)}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                            ${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.payment_method?.name || 'N/A'}</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.reference_number || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Transaction Number</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.transaction?.transaction_number || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date Created</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.formatted_date || new Date(payment.created_at).toLocaleString()}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <p class="mt-1 text-sm text-gray-900">${payment.notes || 'No notes'}</p>
                    </div>
                </div>
            </div>
        `;

        $('#payment-details').html(detailsHtml);

        // Show transaction details if available
        if (payment.transaction) {
            displayTransactionDetails(payment.transaction);
            $('#transaction-card').show();
        }
    }

    // Display transaction details
    function displayTransactionDetails(transaction) {
        const transactionHtml = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Transaction Number</label>
                        <p class="mt-1 text-sm text-gray-900">${transaction.transaction_number}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Order Number</label>
                        <p class="mt-1 text-sm text-gray-900">${transaction.order?.order_number || 'N/A'}</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                        <p class="mt-1 text-sm text-gray-900">$${parseFloat(transaction.total_amount).toFixed(2)}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <p class="mt-1 text-sm text-gray-900">${transaction.status}</p>
                    </div>
                </div>
            </div>
        `;

        $('#transaction-details').html(transactionHtml);
    }

    // Show action buttons based on payment status
    function showActionButtons(payment) {
        $('#actions-card').show();

        if (payment.status === 'completed') {
            $('#refund-payment-btn').show().data('payment-id', payment.id);
        }

        if (payment.status === 'pending') {
            $('#cancel-payment-btn').show().data('payment-id', payment.id);
        }
    }

    // Show error message
    function showError(message) {
        $('#payment-details').html(`
            <div class="text-center py-12">
                <div class="text-red-600 text-lg font-medium">${message}</div>
            </div>
        `);
    }

    // Handle cancel payment
    $('#cancel-payment-btn').on('click', function() {
        const paymentId = $(this).data('payment-id');
        
        Swal.fire({
            title: 'Are you sure?',
            text: 'This will cancel the payment and cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, cancel it!',
            cancelButtonText: 'No, keep it'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `{{ url('api/payments') }}/${paymentId}/cancel`,
                    method: 'POST',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Cancelled!',
                                text: 'Payment has been cancelled.'
                            }).then(() => {
                                loadPaymentDetails(); // Reload details
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: response.message || 'Failed to cancel payment'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error cancelling payment:', xhr);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Failed to cancel payment'
                        });
                    }
                });
            }
        });
    });

    // Handle refund payment
    $('#refund-payment-btn').on('click', function() {
        const paymentId = $(this).data('payment-id');
        
        Swal.fire({
            title: 'Refund Payment',
            input: 'textarea',
            inputLabel: 'Refund Reason',
            inputPlaceholder: 'Enter refund reason...',
            inputAttributes: {
                'aria-label': 'Enter refund reason'
            },
            showCancelButton: true,
            confirmButtonText: 'Refund',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please enter a refund reason!'
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `{{ url('api/payments') }}/${paymentId}/refund`,
                    method: 'POST',
                    data: {
                        reason: result.value
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Refunded!',
                                text: 'Payment has been refunded.'
                            }).then(() => {
                                loadPaymentDetails(); // Reload details
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: response.message || 'Failed to refund payment'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error refunding payment:', xhr);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Failed to refund payment'
                        });
                    }
                });
            }
        });
    });

    // Load payment details on page load
    loadPaymentDetails();
});
</script>
@endpush
