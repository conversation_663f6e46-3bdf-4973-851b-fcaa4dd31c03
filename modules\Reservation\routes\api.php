<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Api\AreaController;
use Modules\Reservation\Http\Controllers\Api\TableController;
use Modules\Reservation\Http\Controllers\Api\QRCodeController;
use Modules\Reservation\Http\Controllers\Api\ReservationController;
use Modules\Reservation\Http\Controllers\Api\WaiterRequestController;
use Modules\Reservation\Http\Controllers\Web\ReservationController as WebReservationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('reservation')->group(function () {
    
    // Reservation Routes
    Route::prefix('reservations')->group(function () {
        Route::get('/', [ReservationController::class, 'index']);
        Route::post('/', [ReservationController::class, 'store']);
        Route::get('/statistics', [ReservationController::class, 'statistics']);
        Route::get('/check-availability', [ReservationController::class, 'checkAvailability']);
        Route::get('/{id}', [ReservationController::class, 'show']);
        Route::put('/{id}', [ReservationController::class, 'update']);
        Route::delete('/{id}', [ReservationController::class, 'destroy']);
        
        // Reservation Status Actions
        Route::post('/{id}/confirm', [ReservationController::class, 'confirm']);
        Route::post('/{id}/seat', [ReservationController::class, 'seat']);
        Route::post('/{id}/complete', [ReservationController::class, 'complete']);
        Route::post('/{id}/no-show', [ReservationController::class, 'noShow']);
    });

    // Area Routes
    Route::prefix('areas')->group(function () {
        Route::get('/', [AreaController::class, 'index']);
        Route::post('/', [AreaController::class, 'store']);
        Route::get('/{id}', [AreaController::class, 'show']);
        Route::put('/{id}', [AreaController::class, 'update']);
        Route::delete('/{id}', [AreaController::class, 'destroy']);
        Route::get('/{id}/tables', [AreaController::class, 'tables']);
        Route::get('/{id}/statistics', [AreaController::class, 'statistics']);
    });

    // Table Routes
    Route::prefix('tables')->group(function () {
        Route::get('/', [TableController::class, 'index']);
        Route::post('/', [TableController::class, 'store']);
        Route::get('/available', [TableController::class, 'available']);
        Route::get('/occupancy', [TableController::class, 'occupancy']);
        Route::get('/qr/{qr_code}', [TableController::class, 'getByQR']);
        Route::get('/{id}', [TableController::class, 'show']);
        Route::put('/{id}', [TableController::class, 'update']);
        Route::delete('/{id}', [TableController::class, 'destroy']);
        Route::patch('/{id}/status', [TableController::class, 'updateStatus']);
        Route::post('/{id}/generate-qr', [TableController::class, 'generateQR']);
    });

    // QR Code Routes
    Route::prefix('qr')->group(function () {
        Route::post('/validate', [QRCodeController::class, 'validateQR']);
        Route::post('/batch-generate', [QRCodeController::class, 'batchGenerate']);
        Route::get('/table/{tableId}/content', [QRCodeController::class, 'getQRContent']);
        
        // Generate specific QR types
        Route::post('/table/{tableId}/generate', [QRCodeController::class, 'generateTableQR']);
        Route::post('/table/{tableId}/menu', [QRCodeController::class, 'generateMenuQR']);
        Route::post('/table/{tableId}/order', [QRCodeController::class, 'generateOrderQR']);
    });

    // Waiter Request routes
    Route::prefix('waiter-requests')->group(function () {
        Route::get('/', [WaiterRequestController::class, 'index']);
        Route::post('/', [WaiterRequestController::class, 'store']);
        Route::get('/waiter', [WaiterRequestController::class, 'getWaiterRequests']);
        Route::get('/table/{tableId}', [WaiterRequestController::class, 'getTableRequests']);
        Route::get('/{id}', [WaiterRequestController::class, 'show']);
        Route::put('/{id}', [WaiterRequestController::class, 'update']);
        Route::delete('/{id}', [WaiterRequestController::class, 'destroy']);
        Route::patch('/{id}/complete', [WaiterRequestController::class, 'complete']);
        Route::patch('/{id}/cancel', [WaiterRequestController::class, 'cancel']);
    });
});

// DataTable API Routes (for frontend DataTables)
Route::middleware(['auth'])->group(function () {
    Route::get('/reservations', [WebReservationController::class, 'reservationsDataTable']);
    Route::get('/areas', [WebReservationController::class, 'areasDataTable']);
    Route::get('/tables', [WebReservationController::class, 'tablesDataTable']);
    Route::get('/waiter-requests', [WebReservationController::class, 'waiterRequestsDataTable']);
    Route::get('/api/waiter-requests-cards', [WebReservationController::class, 'waiterRequestsCards']);
    Route::get('/waiter-requests-cards', [WebReservationController::class, 'waiterRequestsCards']);

    // CRUD Routes for Reservations
    Route::post('/reservations', [WebReservationController::class, 'reservationsStore'])->name('reservations.store');
    Route::get('/reservations/{id}', [WebReservationController::class, 'reservationsShow'])->name('reservations.show');
    Route::get('/reservations/{id}/edit', [WebReservationController::class, 'reservationsEdit'])->name('reservations.edit');
    Route::put('/reservations/{id}', [WebReservationController::class, 'reservationsUpdate'])->name('reservations.update');
    Route::delete('/reservations/{id}', [WebReservationController::class, 'reservationsDestroy'])->name('reservations.destroy');
    Route::post('/reservations/{id}/confirm', [WebReservationController::class, 'reservationsConfirm'])->name('reservations.confirm');

    // CRUD Routes for Areas
    Route::post('/areas', [WebReservationController::class, 'areasStore'])->name('areas.store');
    Route::get('/areas/{id}', [WebReservationController::class, 'areasShow'])->name('areas.show');
    Route::get('/areas/{id}/edit', [WebReservationController::class, 'areasEdit'])->name('areas.edit');
    Route::put('/areas/{id}', [WebReservationController::class, 'areasUpdate'])->name('areas.update');
    Route::delete('/areas/{id}', [WebReservationController::class, 'areasDestroy'])->name('areas.destroy');
    Route::get('/areas/{id}/tables', [WebReservationController::class, 'getAreaTables'])->name('areas.tables');

    // CRUD Routes for Waiter Requests
    Route::post('/waiter-requests', [WebReservationController::class, 'waiterRequestsStore'])->name('waiter-requests.store');
    Route::get('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsShow'])->name('waiter-requests.show');
    Route::get('/waiter-requests/{id}/edit', [WebReservationController::class, 'waiterRequestsEdit'])->name('waiter-requests.edit');
    Route::put('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsUpdate'])->name('waiter-requests.update');
    Route::delete('/waiter-requests/{id}', [WebReservationController::class, 'waiterRequestsDestroy'])->name('waiter-requests.destroy');
    Route::patch('/waiter-requests/{id}/complete', [WebReservationController::class, 'waiterRequestsComplete'])->name('waiter-requests.complete');
    Route::patch('/waiter-requests/{id}/cancel', [WebReservationController::class, 'waiterRequestsCancel'])->name('waiter-requests.cancel');

    // Dropdown/List Routes
    Route::get('/customers', [WebReservationController::class, 'getCustomers'])->name('customers.list');
    Route::get('/areas-list', [WebReservationController::class, 'getAreas'])->name('areas.list');
    Route::get('/tables-list', [WebReservationController::class, 'getTables'])->name('tables.list');
    Route::get('/waiters', [WebReservationController::class, 'getWaiters'])->name('waiters.list');
    Route::get('/reservation-statuses', [WebReservationController::class, 'getReservationStatuses'])->name('reservation-statuses.list');
});