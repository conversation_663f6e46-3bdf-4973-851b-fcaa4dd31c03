1754260864O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:17:"App\Models\Branch":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"branches";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:3;s:4:"name";s:38:"<PERSON><PERSON><PERSON><PERSON><PERSON>, Upton and Hodkiewicz Branch";s:4:"code";s:41:"mcdermott-upton-and-hodkiewicz-branch-695";s:7:"address";s:42:"40715 Labadie Tunnel
Klockoville, NV 50356";s:5:"phone";s:15:"******-354-0412";}s:11:" * original";a:5:{s:2:"id";i:3;s:4:"name";s:38:"McDermott, Upton and Hodkiewicz Branch";s:4:"code";s:41:"mcdermott-upton-and-hodkiewicz-branch-695";s:7:"address";s:42:"40715 Labadie Tunnel
Klockoville, NV 50356";s:5:"phone";s:15:"******-354-0412";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:10:{s:8:"latitude";s:9:"decimal:7";s:9:"longitude";s:9:"decimal:7";s:15:"delivery_radius";s:9:"decimal:2";s:15:"operating_hours";s:5:"array";s:22:"printer_configurations";s:5:"array";s:19:"is_delivery_enabled";s:7:"boolean";s:19:"is_takeaway_enabled";s:7:"boolean";s:18:"is_dine_in_enabled";s:7:"boolean";s:26:"is_online_ordering_enabled";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:21:{i:0;s:9:"tenant_id";i:1;s:4:"name";i:2;s:4:"code";i:3;s:7:"address";i:4;s:8:"latitude";i:5;s:9:"longitude";i:6;s:5:"phone";i:7;s:5:"email";i:8;s:12:"manager_name";i:9;s:16:"seating_capacity";i:10;s:15:"delivery_radius";i:11;s:15:"operating_hours";i:12;s:8:"timezone";i:13;s:19:"is_delivery_enabled";i:14;s:19:"is_takeaway_enabled";i:15;s:18:"is_dine_in_enabled";i:16;s:26:"is_online_ordering_enabled";i:17;s:22:"printer_configurations";i:18;s:15:"pos_terminal_id";i:19;s:6:"status";i:20;s:18:"default_kitchen_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:1;O:17:"App\Models\Branch":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"branches";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:1;s:4:"name";s:37:"Schiller, Kiehn and Schowalter Branch";s:4:"code";s:40:"schiller-kiehn-and-schowalter-branch-209";s:7:"address";s:43:"8285 Elda Orchard
Lake Jovanshire, WI 55324";s:5:"phone";s:17:"+****************";}s:11:" * original";a:5:{s:2:"id";i:1;s:4:"name";s:37:"Schiller, Kiehn and Schowalter Branch";s:4:"code";s:40:"schiller-kiehn-and-schowalter-branch-209";s:7:"address";s:43:"8285 Elda Orchard
Lake Jovanshire, WI 55324";s:5:"phone";s:17:"+****************";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:10:{s:8:"latitude";s:9:"decimal:7";s:9:"longitude";s:9:"decimal:7";s:15:"delivery_radius";s:9:"decimal:2";s:15:"operating_hours";s:5:"array";s:22:"printer_configurations";s:5:"array";s:19:"is_delivery_enabled";s:7:"boolean";s:19:"is_takeaway_enabled";s:7:"boolean";s:18:"is_dine_in_enabled";s:7:"boolean";s:26:"is_online_ordering_enabled";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:21:{i:0;s:9:"tenant_id";i:1;s:4:"name";i:2;s:4:"code";i:3;s:7:"address";i:4;s:8:"latitude";i:5;s:9:"longitude";i:6;s:5:"phone";i:7;s:5:"email";i:8;s:12:"manager_name";i:9;s:16:"seating_capacity";i:10;s:15:"delivery_radius";i:11;s:15:"operating_hours";i:12;s:8:"timezone";i:13;s:19:"is_delivery_enabled";i:14;s:19:"is_takeaway_enabled";i:15;s:18:"is_dine_in_enabled";i:16;s:26:"is_online_ordering_enabled";i:17;s:22:"printer_configurations";i:18;s:15:"pos_terminal_id";i:19;s:6:"status";i:20;s:18:"default_kitchen_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}