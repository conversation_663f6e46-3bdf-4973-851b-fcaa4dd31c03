@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<!-- Select2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<!-- SweetAlert CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="flex justify-between items-center mb-6">
    <div class="flex-1">
        <div class="flex items-center">
            <h4 class="text-xl font-semibold text-gray-800 mb-0">إدارة الحجوزات</h4>
            <span class="text-gray-500 text-sm mr-2 mb-0">/ الحجوزات</span>
        </div>
    </div>
    <div class="flex items-center">
        <div class="pr-1">
            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200" id="add-reservation-btn">
                <i class="mdi mdi-plus mr-2"></i>
                إضافة حجز جديد
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="grid grid-cols-1 gap-6">
    <div class="col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-800 mb-0">قائمة الحجوزات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray-400"></i>
                </div>
                <p class="text-sm text-gray-500 mt-1">إدارة جميع حجوزات المطعم</p>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="reservations-table" class="w-full table-auto border-collapse border border-gray-300" style="width:100%">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="border border-gray-300 px-4 py-2 text-right">#</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">رقم الحجز</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">اسم العميل</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">رقم الهاتف</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">المنطقة</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">الطاولة</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">عدد الأشخاص</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">تاريخ الحجز</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">الحالة</th>
                                <th class="border border-gray-300 px-4 py-2 text-right">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Reservation Modal -->
<div class="modal fade" id="reservationModal" tabindex="-1" role="dialog" aria-labelledby="reservationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl mx-auto">
            <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800" id="reservationModalLabel">إضافة حجز جديد</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <form id="reservationForm">
                <div class="px-6 py-4">
                    <input type="hidden" id="reservation_id" name="reservation_id">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label for="customer_name" class="block text-sm font-medium text-gray-700">اسم العميل <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="customer_name" name="customer_name" required>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                        <div class="space-y-2">
                            <label for="customer_phone" class="block text-sm font-medium text-gray-700">رقم الهاتف <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="customer_phone" name="customer_phone" required>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div class="space-y-2">
                            <label for="customer_email" class="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
                            <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="customer_email" name="customer_email">
                            <div class="text-red-500 text-sm"></div>
                        </div>
                        <div class="space-y-2">
                            <label for="party_size" class="block text-sm font-medium text-gray-700">عدد الأشخاص <span class="text-red-500">*</span></label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="party_size" name="party_size" min="1" required>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div class="space-y-2">
                            <label for="area_id" class="block text-sm font-medium text-gray-700">المنطقة <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="area_id" name="area_id" required>
                                <option value="">اختر المنطقة</option>
                            </select>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                        <div class="space-y-2">
                            <label for="table_id" class="block text-sm font-medium text-gray-700">الطاولة <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="table_id" name="table_id" required>
                                <option value="">اختر الطاولة</option>
                            </select>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div class="space-y-2">
                            <label for="reservation_datetime" class="block text-sm font-medium text-gray-700">تاريخ ووقت الحجز <span class="text-red-500">*</span></label>
                            <input type="datetime-local" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="reservation_datetime" name="reservation_datetime" required>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                        <div class="space-y-2">
                            <label for="duration_minutes" class="block text-sm font-medium text-gray-700">مدة الحجز (بالدقائق)</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="duration_minutes" name="duration_minutes" min="30" value="120">
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="space-y-2">
                            <label for="special_requests" class="block text-sm font-medium text-gray-700">طلبات خاصة</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="special_requests" name="special_requests" rows="3"></textarea>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="space-y-2">
                            <label for="notes" class="block text-sm font-medium text-gray-700">ملاحظات</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="notes" name="notes" rows="3"></textarea>
                            <div class="text-red-500 text-sm"></div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 px-6 py-4 border-t border-gray-200">
                    <button type="button" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200" id="save-reservation-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Reservation Modal -->
<div class="modal fade" id="showReservationModal" tabindex="-1" role="dialog" aria-labelledby="showReservationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl mx-auto">
            <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800" id="showReservationModalLabel">تفاصيل الحجز</h5>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-2xl">&times;</span>
                </button>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">رقم الحجز:</label>
                        <p class="text-gray-600" id="show_reservation_number"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">اسم العميل:</label>
                        <p class="text-gray-600" id="show_customer_name"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">رقم الهاتف:</label>
                        <p class="text-gray-600" id="show_customer_phone"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">البريد الإلكتروني:</label>
                        <p class="text-gray-600" id="show_customer_email"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">المنطقة:</label>
                        <p class="text-gray-600" id="show_area"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">الطاولة:</label>
                        <p class="text-gray-600" id="show_table"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">عدد الأشخاص:</label>
                        <p class="text-gray-600" id="show_party_size"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">تاريخ ووقت الحجز:</label>
                        <p class="text-gray-600" id="show_reservation_datetime"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">مدة الحجز:</label>
                        <p class="text-gray-600" id="show_duration"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">الحالة:</label>
                        <p class="text-gray-600" id="show_status"></p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">طلبات خاصة:</label>
                        <p class="text-gray-600" id="show_special_requests"></p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">ملاحظات:</label>
                        <p class="text-gray-600" id="show_notes"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">تاريخ الإنشاء:</label>
                        <p class="text-gray-600" id="show_created_at"></p>
                    </div>
                    <div class="space-y-2">
                        <label class="block text-sm font-semibold text-gray-700">تاريخ التحديث:</label>
                        <p class="text-gray-600" id="show_updated_at"></p>
                    </div>
                </div>
            </div>
            <div class="flex justify-end px-6 py-4 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.colVis.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#reservationModal')
    });

    // Load Areas
    function loadAreas() {
        $.ajax({
            url: '/reservation/api/areas', // Fixed URL to match the API route
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر المنطقة</option>';
                // The response is a direct array
                if (Array.isArray(response)) {
                    $.each(response, function(index, area) {
                        options += '<option value="' + area.id + '">' + area.name + '</option>';
                    });
                }
                $('#area_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading areas:', xhr);
                $('#area_id').html('<option value="">اختر المنطقة</option>');
            }
        });
    }

    // Load Tables based on selected area
    $('#area_id').change(function() {
        var areaId = $(this).val();
        if (areaId) {
            $.ajax({
                url: '/reservation/api/areas/' + areaId + '/tables', // Fixed URL to match the API route
                type: 'GET',
                success: function(response) {
                    var options = '<option value="">اختر الطاولة</option>';
                    // The endpoint returns {success: true, data: [...]}
                    if (response.success && response.data) {
                        $.each(response.data, function(index, table) {
                            var tableName = table.table_name || ('طاولة ' + table.table_number);
                            options += '<option value="' + table.id + '">' + tableName + ' (سعة: ' + table.seating_capacity + ')</option>';
                        });
                    }
                    $('#table_id').html(options);
                },
                error: function(xhr) {
                    console.log('Error loading tables:', xhr);
                    $('#table_id').html('<option value="">اختر الطاولة</option>');
                }
            });
        } else {
            $('#table_id').html('<option value="">اختر الطاولة</option>');
        }
    });

    // Load areas on page load
    loadAreas();

    // Check if table exists before initializing DataTable
    if ($('#reservations-table').length === 0) {
        console.error('Table with ID reservations-table not found');
        return;
    }

    // Initialize DataTable with server-side processing
    var table = $('#reservations-table').DataTable({
        processing: true,
        serverSide: true,
        destroy: true, // Allow reinitialization
        ajax: {
            url: '/reservations', // Fixed URL to match the API route
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            error: function(xhr, error, code) {
                console.log('DataTable AJAX Error:', xhr, error, code);
                swal('خطأ!', 'حدث خطأ أثناء تحميل البيانات', 'error');
            }
        },
        columns: [
            { 
                data: 'DT_RowIndex', 
                name: 'DT_RowIndex', 
                orderable: false, 
                searchable: false,
                width: '5%'
            },
            { 
                data: 'reservation_number', 
                name: 'reservation_number',
                defaultContent: '-'
            },
            { 
                data: 'customer_name', 
                name: 'customer_name',
                defaultContent: '-'
            },
            { 
                data: 'customer_phone', 
                name: 'customer_phone',
                defaultContent: '-'
            },
            { 
                data: 'area.name', 
                name: 'area.name',
                defaultContent: '-'
            },
            { 
                data: 'table.table_number', 
                name: 'table.table_number',
                defaultContent: '-'
            },
            { 
                data: 'party_size', 
                name: 'party_size',
                defaultContent: '-'
            },
            { 
                data: 'reservation_datetime', 
                name: 'reservation_datetime',
                defaultContent: '-'
            },
            { 
                data: 'status', 
                name: 'status', 
                orderable: false,
                defaultContent: '-'
            },
            { 
                data: 'action', 
                name: 'action', 
                orderable: false, 
                searchable: false,
                width: '15%',
                defaultContent: '-'
            }
        ],
        language: {
            processing: "جاري المعالجة...",
            search: "بحث:",
            lengthMenu: "أظهر _MENU_ مدخلات",
            info: "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            infoEmpty: "يعرض 0 إلى 0 من أصل 0 مدخل",
            infoFiltered: "(منتقاة من مجموع _MAX_ مدخل)",
            infoPostFix: "",
            loadingRecords: "جاري التحميل...",
            zeroRecords: "لم يعثر على أية سجلات",
            emptyTable: "لا توجد بيانات متاحة في الجدول",
            paginate: {
                first: "الأول",
                previous: "السابق",
                next: "التالي",
                last: "الأخير"
            },
            aria: {
                sortAscending: ": تفعيل لترتيب العمود تصاعدياً",
                sortDescending: ": تفعيل لترتيب العمود تنازلياً"
            }
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'copy',
                text: 'نسخ'
            },
            {
                extend: 'csv',
                text: 'CSV'
            },
            {
                extend: 'excel',
                text: 'Excel'
            },
            {
                extend: 'pdf',
                text: 'PDF'
            },
            {
                extend: 'print',
                text: 'طباعة'
            }
        ],
        responsive: true,
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[1, 'desc']] // Order by reservation number descending
    });

    // Add Reservation Button
    $('#add-reservation-btn').click(function() {
        $('#reservationForm')[0].reset();
        $('#reservation_id').val('');
        $('#reservationModalLabel').text('إضافة حجز جديد');
        $('.form-control').removeClass('is-invalid');
        loadAreas();
        $('#reservationModal').modal('show');
    });

    // Edit Reservation Button
    $(document).on('click', '.edit-reservation', function() {
        var id = $(this).data('id');
        
        // First load areas, then get reservation data
        loadAreas();
        
        $.ajax({
            url: '/reservations/' + id, // Fixed URL to match the API route
            type: 'GET',
            success: function(response) {
                var reservation = response.data;
                $('#reservation_id').val(reservation.id);
                $('#customer_name').val(reservation.customer_name);
                $('#customer_phone').val(reservation.customer_phone);
                $('#customer_email').val(reservation.customer_email);
                $('#party_size').val(reservation.party_size);
                
                // Set area and trigger change to load tables
                $('#area_id').val(reservation.area_id);
                
                // Wait a bit for areas to load, then trigger change and set table
                setTimeout(function() {
                    $('#area_id').trigger('change');
                    
                    // Wait for tables to load, then set the table value
                    setTimeout(function() {
                        $('#table_id').val(reservation.table_id);
                    }, 500);
                }, 300);
                
                $('#reservation_datetime').val(reservation.reservation_datetime);
                $('#duration_minutes').val(reservation.duration_minutes);
                $('#special_requests').val(reservation.special_requests);
                $('#notes').val(reservation.notes);
                $('#reservationModalLabel').text('تعديل الحجز');
                $('.form-control').removeClass('is-invalid');
                $('#reservationModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الحجز', 'error');
            }
        });
    });

    // Show Reservation Button
    $(document).on('click', '.show-reservation', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '/reservations/' + id, // Fixed URL to match the API route
            type: 'GET',
            success: function(response) {
                var reservation = response.data;
                $('#show_reservation_number').text(reservation.reservation_number || '-');
                $('#show_customer_name').text(reservation.customer_name || '-');
                $('#show_customer_phone').text(reservation.customer_phone || '-');
                $('#show_customer_email').text(reservation.customer_email || '-');
                $('#show_area').text(reservation.area ? reservation.area.name : '-');
                $('#show_table').text(reservation.table ? reservation.table.table_number : '-');
                $('#show_party_size').text(reservation.party_size || '-');
                $('#show_reservation_datetime').text(new Date(reservation.reservation_datetime).toLocaleString('ar-SA'));
                $('#show_duration').text(reservation.formatted_duration || '-');
                $('#show_status').html(getStatusBadge(reservation.reservation_status));
                $('#show_special_requests').text(reservation.special_requests || '-');
                $('#show_notes').text(reservation.notes || '-');
                $('#show_created_at').text(new Date(reservation.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(reservation.updated_at).toLocaleDateString('ar-SA'));
                $('#showReservationModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الحجز', 'error');
            }
        });
    });

    // Delete Reservation Button
    $(document).on('click', '.delete-reservation', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '/reservations/' + id, // Fixed URL to match the API route
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف الحجز بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف الحجز", "error");
                }
            });
        });
    });

    // Confirm Reservation Button
    $(document).on('click', '.confirm-reservation', function() {
        var id = $(this).data('id');
        
        swal({
            title: "تأكيد الحجز",
            text: "هل تريد تأكيد هذا الحجز؟",
            type: "info",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            confirmButtonText: "نعم، أكد!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '/reservations/' + id + '/confirm', // Fixed URL to match the API route
                type: 'PUT',
                success: function(response) {
                    swal("تم التأكيد!", "تم تأكيد الحجز بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء تأكيد الحجز", "error");
                }
            });
        });
    });

    // Save Reservation Form
    $('#reservationForm').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var id = $('#reservation_id').val();
        var url = id ? '/reservations/' + id : '/reservations';
        var method = id ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#reservationModal').modal('hide');
                swal("نجح!", id ? "تم تحديث الحجز بنجاح" : "تم إضافة الحجز بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ الحجز", "error");
                }
            }
        });
    });

    // Helper function to get status badge
    function getStatusBadge(status) {
        if (!status) return '-';
        
        var badgeClass = 'badge-secondary';
        switch(status.name) {
            case 'pending':
                badgeClass = 'badge-warning';
                break;
            case 'confirmed':
                badgeClass = 'badge-info';
                break;
            case 'seated':
                badgeClass = 'badge-success';
                break;
            case 'completed':
                badgeClass = 'badge-primary';
                break;
            case 'cancelled':
                badgeClass = 'badge-danger';
                break;
        }
        
        return '<span class="badge ' + badgeClass + '">' + status.name + '</span>';
    }
});
</script>
@endsection