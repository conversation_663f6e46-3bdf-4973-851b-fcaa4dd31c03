@extends('layouts.master')

@section('title', 'إضافة فرع جديد')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Header -->
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">إضافة فرع جديد</h3>
                <a href="{{ route('branches.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    العودة للقائمة
                </a>
            </div>
        </div>
        
        <!-- Form -->
        <form action="{{ route('branches.store') }}" method="POST" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h5 class="text-base font-semibold text-gray-900 mb-4">المعلومات الأساسية</h5>
                    </div>
                    
                    <!-- Tenant Selection -->
                    <div>
                        <label for="tenant_id" class="block text-sm font-medium text-gray-700 mb-2">
                            المستأجر <span class="text-red-500">*</span>
                        </label>
                        <select name="tenant_id" id="tenant_id" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('tenant_id') border-red-500 @enderror" 
                                required>
                            <option value="">اختر المستأجر</option>
                            @foreach($tenants as $tenant)
                                <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                    {{ $tenant->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('tenant_id')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Branch Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم الفرع <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('name') border-red-500 @enderror" 
                               value="{{ old('name') }}" required>
                        @error('name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Branch Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            كود الفرع
                        </label>
                        <input type="text" name="code" id="code" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('code') border-red-500 @enderror" 
                               value="{{ old('code') }}" 
                               placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                        @error('code')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Address -->
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            العنوان <span class="text-red-500">*</span>
                        </label>
                        <textarea name="address" id="address" rows="3"
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('address') border-red-500 @enderror" 
                                  required>{{ old('address') }}</textarea>
                        @error('address')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h5 class="text-base font-semibold text-gray-900 mb-4">معلومات الاتصال</h5>
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="phone" id="phone" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('phone') border-red-500 @enderror" 
                               value="{{ old('phone') }}" required>
                        @error('phone')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" name="email" id="email" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('email') border-red-500 @enderror" 
                               value="{{ old('email') }}">
                        @error('email')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Manager Name -->
                    <div>
                        <label for="manager_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم مدير الفرع <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="manager_name" id="manager_name" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('manager_name') border-red-500 @enderror" 
                               value="{{ old('manager_name') }}" required>
                        @error('manager_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Timezone -->
                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            المنطقة الزمنية <span class="text-red-500">*</span>
                        </label>
                        <select name="timezone" id="timezone" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('timezone') border-red-500 @enderror" 
                                required>
                            <option value="">اختر المنطقة الزمنية</option>
                            <option value="Asia/Riyadh" {{ old('timezone') == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (GMT+3)</option>
                            <option value="Asia/Dubai" {{ old('timezone') == 'Asia/Dubai' ? 'selected' : '' }}>دبي (GMT+4)</option>
                            <option value="Asia/Kuwait" {{ old('timezone') == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (GMT+3)</option>
                            <option value="Asia/Qatar" {{ old('timezone') == 'Asia/Qatar' ? 'selected' : '' }}>قطر (GMT+3)</option>
                            <option value="Asia/Bahrain" {{ old('timezone') == 'Asia/Bahrain' ? 'selected' : '' }}>البحرين (GMT+3)</option>
                        </select>
                        @error('timezone')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Divider -->
            <div class="my-8 border-t border-gray-200"></div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Branch Settings -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h5 class="text-base font-semibold text-gray-900 mb-4">إعدادات الفرع</h5>
                    </div>
                    
                    <!-- Seating Capacity -->
                    <div>
                        <label for="seating_capacity" class="block text-sm font-medium text-gray-700 mb-2">
                            سعة الجلوس
                        </label>
                        <input type="number" name="seating_capacity" id="seating_capacity" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('seating_capacity') border-red-500 @enderror" 
                               value="{{ old('seating_capacity') }}" min="1">
                        @error('seating_capacity')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Delivery Radius -->
                    <div>
                        <label for="delivery_radius" class="block text-sm font-medium text-gray-700 mb-2">
                            نطاق التوصيل (كم)
                        </label>
                        <input type="number" name="delivery_radius" id="delivery_radius" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('delivery_radius') border-red-500 @enderror" 
                               value="{{ old('delivery_radius') }}" min="0" step="0.1">
                        @error('delivery_radius')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Services -->
                <div class="space-y-6">
                    <div class="border-b border-gray-200 pb-4">
                        <h5 class="text-base font-semibold text-gray-900 mb-4">الخدمات المتاحة</h5>
                    </div>
                    
                    <div class="space-y-4">
                        <!-- Dine In -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_dine_in_enabled" id="is_dine_in_enabled" 
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   value="1" {{ old('is_dine_in_enabled') ? 'checked' : '' }}>
                            <label for="is_dine_in_enabled" class="mr-3 text-sm font-medium text-gray-700">
                                تناول في المكان
                            </label>
                        </div>

                        <!-- Takeaway -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_takeaway_enabled" id="is_takeaway_enabled" 
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   value="1" {{ old('is_takeaway_enabled') ? 'checked' : '' }}>
                            <label for="is_takeaway_enabled" class="mr-3 text-sm font-medium text-gray-700">
                                استلام
                            </label>
                        </div>

                        <!-- Delivery -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_delivery_enabled" id="is_delivery_enabled" 
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   value="1" {{ old('is_delivery_enabled') ? 'checked' : '' }}>
                            <label for="is_delivery_enabled" class="mr-3 text-sm font-medium text-gray-700">
                                توصيل
                            </label>
                        </div>

                        <!-- Online Ordering -->
                        <div class="flex items-center">
                            <input type="checkbox" name="is_online_ordering_enabled" id="is_online_ordering_enabled" 
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   value="1" {{ old('is_online_ordering_enabled') ? 'checked' : '' }}>
                            <label for="is_online_ordering_enabled" class="mr-3 text-sm font-medium text-gray-700">
                                طلب أونلاين
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="submit" 
                            class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200 shadow-sm">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        حفظ
                    </button>
                    <a href="{{ route('branches.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 text-sm font-medium rounded-md transition-colors duration-200">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection