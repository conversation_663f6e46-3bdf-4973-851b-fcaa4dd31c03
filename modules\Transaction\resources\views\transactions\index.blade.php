@extends('layouts.master')

@section('title', 'Transaction Management')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS for Tailwind -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Transaction Management</h1>
                    <p class="mt-2 text-sm text-gray-600">Manage all restaurant transactions and payments</p>
                </div>
                <button type="button" id="add-transaction-btn" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <i class="fas fa-plus mr-2"></i> Add New Transaction
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100 text-sm font-medium">Total Transactions</p>
                            <p class="text-white text-3xl font-bold" id="total-transactions">0</p>
                        </div>
                        <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-receipt text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-red-100 text-sm font-medium">Due Amount</p>
                            <p class="text-white text-3xl font-bold" id="total-due">$0</p>
                        </div>
                        <div class="bg-red-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100 text-sm font-medium">Paid Amount</p>
                            <p class="text-white text-3xl font-bold" id="total-paid">$0</p>
                        </div>
                        <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-check-circle text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-yellow-100 text-sm font-medium">Total Amount</p>
                            <p class="text-white text-3xl font-bold" id="total-amount">$0</p>
                        </div>
                        <div class="bg-yellow-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-dollar-sign text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Filters Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status:</label>
                        <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Statuses</option>
                            <option value="due">Due</option>
                            <option value="partially_paid">Partially Paid</option>
                            <option value="paid">Paid</option>
                            <option value="overpaid">Overpaid</option>
                            <option value="refunded">Refunded</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label for="date-from-filter" class="block text-sm font-medium text-gray-700 mb-2">Date From:</label>
                        <input type="date" id="date-from-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="date-to-filter" class="block text-sm font-medium text-gray-700 mb-2">Date To:</label>
                        <input type="date" id="date-to-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end space-x-2">
                        <button id="clear-filters" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                            Clear
                        </button>
                        <button id="apply-filters" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                            Apply
                        </button>
                        <button id="refresh-transactions" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                            <i class="fas fa-refresh mr-1"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Transactions</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table id="transactions-table" class="min-w-full divide-y divide-gray-200" data-page-length='25'>
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction #</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Transaction Modal -->
<div id="addTransactionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Add New Transaction</h3>
                <button type="button" id="closeAddTransactionModal" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="addTransactionForm" class="mt-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="order_id" class="block text-sm font-medium text-gray-700 mb-2">Order Number</label>
                        <select id="order_id" name="order_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Order</option>
                        </select>
                    </div>
                    <div>
                        <label for="transaction_type" class="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                        <select id="transaction_type" name="type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Type</option>
                            <option value="sale">Sale</option>
                            <option value="refund">Refund</option>
                            <option value="adjustment">Adjustment</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="total_amount" class="block text-sm font-medium text-gray-700 mb-2">Total Amount</label>
                        <input type="number" id="total_amount" name="total_amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="paid_amount" class="block text-sm font-medium text-gray-700 mb-2">Paid Amount</label>
                        <input type="number" id="paid_amount" name="paid_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <label for="transaction_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="transaction_status" name="status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label for="transaction_date" class="block text-sm font-medium text-gray-700 mb-2">Transaction Date</label>
                        <input type="datetime-local" id="transaction_date" name="transaction_date" value="{{ date('Y-m-d\TH:i') }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="3" placeholder="Additional notes..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end pt-6 border-t border-gray-200 mt-6 space-x-2">
                    <button type="button" id="cancelAddTransaction" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                        Save Transaction
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.tailwindcss.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let table;
    let selectedTransaction = null;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#transactions-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("api.transactions.index") }}',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.date_from = $('#date-from-filter').val();
                    d.date_to = $('#date-to-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'transaction_number', name: 'transaction_number' },
                { data: 'order_number', name: 'order.order_number' },
                { data: 'status_badge', name: 'status' },
                { data: 'formatted_total', name: 'total_amount' },
                { data: 'formatted_paid', name: 'paid_amount' },
                { data: 'formatted_due', name: 'due_amount' },
                { data: 'formatted_date', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"flex items-center space-x-2"B><"flex items-center"f>>rtip',
            buttons: {
                dom: {
                    button: {
                        className: 'inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                    }
                },
                buttons: [
                    {
                        extend: 'copy',
                        text: '<i class="fas fa-copy mr-1"></i> Copy'
                    },
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv mr-1"></i> CSV'
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel mr-1"></i> Excel'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf mr-1"></i> PDF'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print mr-1"></i> Print'
                    }
                ]
            },
            order: [[7, 'desc']], // Order by date descending
            language: {
                processing: '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>',
                emptyTable: '<div class="text-center py-4 text-gray-500">No transactions found</div>',
                zeroRecords: '<div class="text-center py-4 text-gray-500">No matching transactions found</div>'
            }
        });
    }

    // Modal functions
    function showAddTransactionModal() {
        $('#addTransactionModal').removeClass('hidden');
        loadOrders();
    }

    function hideAddTransactionModal() {
        $('#addTransactionModal').addClass('hidden');
        $('#addTransactionForm')[0].reset();
        $('#order_id').html('<option value="">Select Order</option>');
    }

    // Modal event handlers
    $('#add-transaction-btn').on('click', showAddTransactionModal);
    $('#closeAddTransactionModal, #cancelAddTransaction').on('click', hideAddTransactionModal);

    // Close modal when clicking outside
    $('#addTransactionModal').on('click', function(e) {
        if (e.target === this) {
            hideAddTransactionModal();
        }
    });

    // Load orders for the dropdown
    function loadOrders() {
        $.ajax({
            url: '{{ route("orders.index") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    let options = '<option value="">Select Order</option>';
                    response.data.forEach(function(order) {
                        options += `<option value="${order.id}">${order.order_number} - ${order.customer_name || 'No Customer'}</option>`;
                    });
                    $('#order_id').html(options);
                }
            },
            error: function(xhr) {
                console.error('Error loading orders:', xhr);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load orders'
                });
            }
        });
    }

    // Handle Add Transaction form submission
    $('#addTransactionForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();

        // Show loading state
        submitButton.prop('disabled', true).text('Saving...');

        $.ajax({
            url: '{{ route("api.transactions.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: 'Transaction created successfully'
                    });

                    // Reset form and close modal
                    hideAddTransactionModal();

                    // Refresh table and statistics
                    table.draw();
                    loadStatistics();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message || 'Failed to create transaction'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error creating transaction:', xhr);
                let errorMessage = "Failed to create transaction";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: errorMessage
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Initialize table
    initializeDataTable();

    // Filter functionality
    $('#apply-filters').on('click', function() {
        table.draw();
    });

    $('#clear-filters').on('click', function() {
        $('#status-filter, #date-from-filter, #date-to-filter').val('');
        table.draw();
    });

    $('#refresh-transactions').on('click', function() {
        table.draw();
        loadStatistics();
    });

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("api.transactions.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    $('#total-transactions').text(response.data.total_transactions || 0);
                    $('#total-due').text('$' + (response.data.total_due || 0).toFixed(2));
                    $('#total-paid').text('$' + (response.data.total_paid || 0).toFixed(2));
                    $('#total-amount').text('$' + (response.data.total_amount || 0).toFixed(2));
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load statistics'
                });
            }
        });
    }

    // Initial statistics load
    loadStatistics();
});
</script>
@endpush
