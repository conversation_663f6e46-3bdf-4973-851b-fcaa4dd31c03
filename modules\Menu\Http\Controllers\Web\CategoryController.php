<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreCategoryRequest;
use Modules\Menu\Http\Requests\UpdateCategoryRequest;
use Modules\Menu\Services\CategoryService;
use Modules\Menu\Services\MenuService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class CategoryController extends Controller
{
    protected $categoryService;
    protected $menuService;

    public function __construct(CategoryService $categoryService, MenuService $menuService)
    {
        $this->categoryService = $categoryService;
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of categories for DataTable
     */
    public function index(Request $request)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId() ?? 11; // Default to branch 11 for testing
            
            if ($request->ajax()) {
                return $this->categoryService->getCategoriesForDataTable($branchId, $request);
            }

            return view('menu::categories');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve categories: ' . $e->getMessage());
        }
    }



    /**
     * Store a newly created category
     */
    public function store(StoreCategoryRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $category = $this->categoryService->createCategoryForWeb($data);
            
            return ResponseHelper::success('Category created successfully', $category);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create category: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified category
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $category = $this->categoryService->getCategoryByIdForBranch($id, $branchId);
            
            if (!$category) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success('Category retrieved successfully', $category);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve category: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $category = $this->categoryService->getCategoryByIdForBranch($id, $branchId);
            
            if (!$category) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success('Category retrieved for editing', $category);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve category: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified category
     */
    public function update(UpdateCategoryRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $category = $this->categoryService->updateCategoryForWeb($id, $request->validated(), $branchId);
            
            if (!$category) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success('Category updated successfully', $category);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update category: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified category
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->categoryService->deleteCategoryForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success('Category deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete category: ' . $e->getMessage());
        }
    }

    /**
     * Get categories list for dropdowns
     */
    public function getCategoriesList(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuId = $request->get('menu_id');
            $categories = $this->categoryService->getCategoriesListForBranch($branchId, $menuId);
            
            return ResponseHelper::success('Categories list retrieved successfully', $categories);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve categories list: ' . $e->getMessage());
        }
    }

    /**
     * Get menus list for dropdowns
     */
    public function getMenusList(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menus = $this->menuService->getMenusListForBranch($branchId);
            
            return ResponseHelper::success('Menus list retrieved successfully', $menus);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menus list: ' . $e->getMessage());
        }
    }
}