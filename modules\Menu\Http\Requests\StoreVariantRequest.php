<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\BranchHelper;

class StoreVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        return [
            'menu_item_id' => [
                'required',
                'exists:menu_items,id',
            ],
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50',
            'price_modifier' => 'required|numeric',
            'cost_modifier' => 'nullable|numeric',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'معرف عنصر القائمة مطلوب',
            'menu_item_id.exists' => 'عنصر القائمة المحدد غير موجود',
            'name.required' => 'اسم التنويع مطلوب',
            'name.string' => 'اسم التنويع يجب أن يكون نص',
            'name.max' => 'اسم التنويع يجب ألا يتجاوز 100 حرف',
            'code.required' => 'كود التنويع مطلوب',
            'code.string' => 'كود التنويع يجب أن يكون نص',
            'code.max' => 'كود التنويع يجب ألا يتجاوز 50 حرف',
            'price_modifier.required' => 'معدل السعر مطلوب',
            'price_modifier.numeric' => 'معدل السعر يجب أن يكون رقم',
            'cost_modifier.numeric' => 'معدل التكلفة يجب أن يكون رقم',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}