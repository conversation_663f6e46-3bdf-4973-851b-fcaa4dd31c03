@extends('layouts.master')

@push('styles')
    <!-- DataTables CSS with Tailwind -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
    
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css">
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة المتغيرات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ المتغيرات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-variation-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Page Header -->
<div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">إدارة التنويعات</h1>
            <p class="text-sm text-gray-600 mt-1">إدارة تنويعات عناصر القائمة</p>
        </div>
        <button type="button" id="add-variation-btn" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            إضافة تنويع جديد
        </button>
    </div>
</div>

<!-- Filters Section -->
<div class="p-6 border-b border-gray-200">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Menu Item Filter -->
        <div>
            <label for="menu_item_filter" class="block text-sm font-medium text-gray-700 mb-2">عنصر القائمة</label>
            <select id="menu_item_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option value="">جميع عناصر القائمة</option>
            </select>
        </div>

        <!-- Status Filter -->
        <div>
            <label for="status_filter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
            <select id="status_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option value="">جميع الحالات</option>
                <option value="1">نشط</option>
                <option value="0">غير نشط</option>
            </select>
        </div>

        <!-- Default Filter -->
        <div>
            <label for="default_filter" class="block text-sm font-medium text-gray-700 mb-2">افتراضي</label>
            <select id="default_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option value="">الكل</option>
                <option value="1">افتراضي</option>
                <option value="0">عادي</option>
            </select>
        </div>

        <!-- Reset Button -->
        <div class="flex items-end">
            <button type="button" id="reset-filters" class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200">
                إعادة تعيين الفلاتر
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="p-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="variations-table" class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">#</th>
                            <th class="px-6 py-3">عنصر القائمة</th>
                            <th class="px-6 py-3">الاسم</th>
                            <th class="px-6 py-3">الكود</th>
                            <th class="px-6 py-3">افتراضي</th>
                            <th class="px-6 py-3">السعر</th>
                            <th class="px-6 py-3">التكلفة</th>
                            <th class="px-6 py-3">الحالة</th>
                            <th class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Variation Modal -->
<div id="variation-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900" id="modal-title">إضافة تنويع جديد</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('variation-modal')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-4">
                <form id="variation-form">
                    <input type="hidden" id="variation_id" name="variation_id">
                    
                    <!-- Basic Information Section -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            المعلومات الأساسية
                        </h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="menu_item_id" class="block text-sm font-medium text-gray-700 mb-2">عنصر القائمة <span class="text-red-500">*</span></label>
                                <select id="menu_item_id" name="menu_item_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                                    <option value="">اختر عنصر القائمة</option>
                                </select>
                                <div class="text-red-500 text-sm mt-1 hidden" id="menu_item_id_error"></div>
                            </div>

                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم التنويع <span class="text-red-500">*</span></label>
                                <input type="text" id="name" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required maxlength="100">
                                <div class="text-red-500 text-sm mt-1 hidden" id="name_error"></div>
                            </div>

                            <div>
                                <label for="code" class="block text-sm font-medium text-gray-700 mb-2">كود التنويع <span class="text-gray-500 text-xs">(يتم إنشاؤه تلقائياً)</span></label>
                                <input type="text" id="code" name="code" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50" placeholder="سيتم إنشاؤه تلقائياً من الاسم" readonly maxlength="50">
                                <p class="text-gray-500 text-xs mt-1">سيتم إنشاء الكود تلقائياً بناءً على اسم التنويع</p>
                                <div class="text-red-500 text-sm mt-1 hidden" id="code_error"></div>
                            </div>

                            <div>
                                <label for="is_default" class="block text-sm font-medium text-gray-700 mb-2">افتراضي</label>
                                <select id="is_default" name="is_default" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="0">لا</option>
                                    <option value="1">نعم</option>
                                </select>
                                <div class="text-red-500 text-sm mt-1 hidden" id="is_default_error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Section -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            معلومات التسعير
                        </h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="price_modifier" class="block text-sm font-medium text-gray-700 mb-2">معدل السعر <span class="text-red-500">*</span></label>
                                <input type="number" step="0.01" id="price_modifier" name="price_modifier" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                                <p class="text-gray-500 text-xs mt-1">القيمة المضافة أو المخصومة من السعر الأساسي</p>
                                <div class="text-red-500 text-sm mt-1 hidden" id="price_modifier_error"></div>
                            </div>

                            <div>
                                <label for="cost_modifier" class="block text-sm font-medium text-gray-700 mb-2">معدل التكلفة</label>
                                <input type="number" step="0.01" id="cost_modifier" name="cost_modifier" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-gray-500 text-xs mt-1">القيمة المضافة أو المخصومة من التكلفة الأساسية</p>
                                <div class="text-red-500 text-sm mt-1 hidden" id="cost_modifier_error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            الإعدادات
                        </h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                <input type="number" id="sort_order" name="sort_order" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <div class="text-red-500 text-sm mt-1 hidden" id="sort_order_error"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end pt-4 border-t border-gray-200 space-x-2 space-x-reverse">
                <button type="button" onclick="hideModal('variation-modal')" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 text-sm font-medium rounded-lg transition-colors duration-200">
                    إلغاء
                </button>
                <button type="button" id="save-variation-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Show Variation Modal -->
<div id="show-variation-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل التنويع</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="hideModal('show-variation-modal')">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-4">
                <!-- Basic Information Section -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        المعلومات الأساسية
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">عنصر القائمة:</label>
                            <p id="show_menu_item" class="text-gray-900"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم التنويع:</label>
                            <p id="show_name" class="text-gray-900"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">كود التنويع:</label>
                            <p id="show_code" class="text-gray-900 font-mono"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">افتراضي:</label>
                            <p id="show_is_default" class="text-gray-900"></p>
                        </div>
                    </div>
                </div>

                <!-- Pricing Section -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        التسعير
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">السعر الإضافي:</label>
                            <p id="show_price" class="text-gray-900 font-semibold"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">التكلفة الإضافية:</label>
                            <p id="show_cost" class="text-gray-900 font-semibold"></p>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        الإعدادات والحالة
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحالة:</label>
                            <p id="show_is_active" class="text-gray-900"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض:</label>
                            <p id="show_sort_order" class="text-gray-900"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء:</label>
                            <p id="show_created_at" class="text-gray-900"></p>
                        </div>

                        <div class="bg-gray-50 p-3 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحديث:</label>
                            <p id="show_updated_at" class="text-gray-900"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end pt-4 border-t border-gray-200">
                <button type="button" onclick="hideModal('show-variation-modal')" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 text-sm font-medium rounded-lg transition-colors duration-200">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <!-- DataTables JS with Tailwind -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <script>
        // Modal helper functions
        function showModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // Error handling functions
        function clearErrors() {
            document.querySelectorAll('.text-red-500').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
                el.classList.add('border-gray-300');
            });
        }

        function showErrors(errors) {
            Object.keys(errors).forEach(field => {
                const errorElement = document.getElementById(field + '_error');
                const inputElement = document.getElementById(field);
                
                if (errorElement) {
                    errorElement.textContent = errors[field][0];
                    errorElement.classList.remove('hidden');
                }
                
                if (inputElement) {
                    inputElement.classList.remove('border-gray-300');
                    inputElement.classList.add('border-red-500');
                }
            });
        }

        $(document).ready(function() {
            // Add CSRF token to all AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Initialize DataTable with server-side processing
            var table = $('#variations-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route("variations.index") }}',
                    data: function(d) {
                        d.menu_item_filter = $('#menu_item_filter').val();
                        d.status_filter = $('#status_filter').val();
                        d.default_filter = $('#default_filter').val();
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                    { data: 'menu_item_name', name: 'menu_item.name' },
                    { data: 'name', name: 'name' },
                    { data: 'code', name: 'code' },
                    { data: 'is_default', name: 'is_default', orderable: false },
                    { data: 'price', name: 'price_modifier' },
                    { data: 'cost', name: 'cost_modifier' },
                    { data: 'is_active', name: 'is_active', orderable: false },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
                responsive: true,
            
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'csv', 'excel', 'pdf', 'print'
                ]
            });

            // Filter event handlers
            $('#menu_item_filter, #status_filter, #default_filter').on('change', function() {
                table.ajax.reload();
            });

            // Reset filters
            $('#reset-filters').on('click', function() {
                $('#menu_item_filter').val('');
                $('#status_filter').val('');
                $('#default_filter').val('');
                table.ajax.reload();
            });

            // Load menu items for dropdown
            function loadMenuItems() {
                $.ajax({
                    url: '{{ route("variations.menu-items-list") }}',
                    type: 'GET',
                    success: function(response) {
                        var formOptions = '<option value="">اختر عنصر القائمة</option>';
                        var filterOptions = '<option value="">جميع عناصر القائمة</option>';
                        
                        $.each(response, function(index, item) {
                            formOptions += '<option value="' + item.id + '">' + item.name + ' (' + item.price + ' ريال)</option>';
                            filterOptions += '<option value="' + item.id + '">' + item.name + '</option>';
                        });
                        
                        $('#menu_item_id').html(formOptions);
                        $('#menu_item_filter').html(filterOptions);
                    },
                    error: function(xhr) {
                        console.log('Error loading menu items:', xhr);
                    }
                });
            }

            // Load menu items on page load
            loadMenuItems();

            // Add Variation Button
            $('#add-variation-btn').click(function() {
                $('#variation-form')[0].reset();
                $('#variation_id').val('');
                $('#modal-title').text('إضافة تنويع جديد');
                clearErrors();
                $('#code').val(''); // Clear code field for new variations
                loadMenuItems(); // Load menu items when opening modal
                showModal('variation-modal');
            });

            // Generate code preview when name changes
            $('#name').on('input', function() {
                var name = $(this).val();
                if (name && !$('#variation_id').val()) { // Only for new variations
                    var code = generateCodeFromName(name, 'VAR');
                    $('#code').val(code);
                }
            });

            // Function to generate code from name
            function generateCodeFromName(name, prefix) {
                // Remove Arabic diacritics and special characters
                var cleanName = name.replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, '');
                
                // Map common Arabic words to English
                var arabicToEnglish = {
                    'كبير': 'LARGE',
                    'صغير': 'SMALL',
                    'متوسط': 'MEDIUM',
                    'عادي': 'REGULAR',
                    'مميز': 'PREMIUM',
                    'حار': 'SPICY',
                    'معتدل': 'MILD',
                    'بارد': 'COLD',
                    'ساخن': 'HOT',
                    'مقلي': 'FRIED',
                    'مشوي': 'GRILLED',
                    'مسلوق': 'BOILED',
                    'نيء': 'RAW',
                    'مطبوخ': 'COOKED'
                };
                
                var words = cleanName.split(' ');
                var codeWords = [];
                
                words.forEach(function(word) {
                    word = word.trim();
                    if (word) {
                        if (arabicToEnglish[word]) {
                            codeWords.push(arabicToEnglish[word]);
                        } else {
                            // Take first 3 characters and convert to uppercase
                            codeWords.push(word.substring(0, 3).toUpperCase());
                        }
                    }
                });
                
                var baseCode = codeWords.join('_');
                return prefix + '_' + baseCode;
            }

            // Edit Variation Button
            $(document).on('click', '.edit-variation', function() {
                var id = $(this).data('id');
                
                $.ajax({
                    url: '{{ route("variations.edit", ":id") }}'.replace(':id', id),
                    type: 'GET',
                    success: function(response) {
                        loadMenuItems(); // Load menu items first
                        setTimeout(function() {
                             $('#variation_id').val(response.id);
                             $('#menu_item_id').val(response.menu_item_id);
                             $('#name').val(response.name);
                             $('#code').val(response.code);
                             $('#price_modifier').val(response.price_modifier);
                             $('#cost_modifier').val(response.cost_modifier);
                             $('#is_default').val(response.is_default);
                             $('#sort_order').val(response.sort_order);
                             $('#modal-title').text('تعديل التنويع');
                             clearErrors();
                             showModal('variation-modal');
                         }, 500); // Wait for menu items to load
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: 'حدث خطأ أثناء جلب بيانات التنويع'
                        });
                    }
                });
            });

            // Show Variation Button
            $(document).on('click', '.show-variation', function() {
                var id = $(this).data('id');
                
                $.ajax({
                    url: '{{ route("variations.show", ":id") }}'.replace(':id', id),
                    type: 'GET',
                    success: function(response) {
                        var variation = response;
                        
                        // Populate variation details
                        $('#show_menu_item').text(variation.menu_item ? variation.menu_item.name : 'غير محدد');
                        $('#show_name').text(variation.name || '-');
                        $('#show_code').text(variation.code || '-');
                        $('#show_price').text(variation.price_modifier ? variation.price_modifier + ' ريال' : '-');
                        $('#show_cost').text(variation.cost_modifier ? variation.cost_modifier + ' ريال' : '-');
                        $('#show_is_default').html(variation.is_default == 1 ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">افتراضي</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">عادي</span>');
                        $('#show_is_active').html(variation.is_active == 1 ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>');
                        $('#show_sort_order').text(variation.sort_order || '-');
                        $('#show_created_at').text(new Date(variation.created_at).toLocaleDateString('ar-SA'));
                        $('#show_updated_at').text(new Date(variation.updated_at).toLocaleDateString('ar-SA'));
                        showModal('show-variation-modal');
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: 'حدث خطأ أثناء جلب بيانات التنويع'
                        });
                    }
                });
            });

            // Delete Variation Button
            $(document).on('click', '.delete-variation', function() {
                var id = $(this).data('id');
                
                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: 'لن تتمكن من التراجع عن هذا الإجراء!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'نعم، احذف!',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '{{ route("variations.destroy", ":id") }}'.replace(':id', id),
                            type: 'DELETE',
                            success: function(response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'تم الحذف!',
                                    text: 'تم حذف التنويع بنجاح.'
                                });
                                table.ajax.reload();
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'خطأ!',
                                    text: 'حدث خطأ أثناء حذف التنويع'
                                });
                            }
                        });
                    }
                });
            });

            // Save Variation Form
            $('#save-variation-btn').click(function(e) {
                e.preventDefault();

                var formData = new FormData(document.getElementById('variation-form'));
                var id = $('#variation_id').val();
                var url = id ? '{{ route("variations.update", ":id") }}'.replace(':id', id) : '{{ route("variations.store") }}';
                var method = id ? 'PUT' : 'POST';

                // Add CSRF token to form data
                formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                if (method === 'PUT') {
                    formData.append('_method', 'PUT');
                }
                
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        hideModal('variation-modal');
                        Swal.fire({
                            icon: 'success',
                            title: 'نجح!',
                            text: id ? 'تم تحديث التنويع بنجاح' : 'تم إضافة التنويع بنجاح'
                        });
                        table.ajax.reload();
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            clearErrors();
                            showErrors(errors);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ!',
                                text: 'حدث خطأ أثناء حفظ التنويع'
                            });
                        }
                    }
                });
            });
        });
    </script>
@endpush